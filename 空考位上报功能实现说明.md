# 空考位上报功能实现说明

## 功能概述

本次实现完成了空考位上报功能，将接收到的MQTT消息数据写入或更新到`ks_kkw_msg`（考试空考位消息）和`ks_ksrcxx`（考生入场信息）表中。

## 实现的功能

### 1. 数据结构完善

#### 完善的DTO类：
- **SbkkwReq**: 空考位上报请求类
- **SbkkwItemDTO**: 空考位数据项
- **KwlbDTO**: 考位列表DTO
- **KzwhxqDTO**: 考生位号详情DTO
- **YsbhyxxDTO**: 已上报核验信息DTO
- **YsbrcxxDTO**: 已上报入场信息DTO

### 2. 核心业务逻辑

#### MqttSbkkwEventDbHandler 主要功能：

1. **数据解密**: 使用SM4算法解密接收到的数据，密钥为`hssfhy@2025~$#@!`

2. **空考位数据处理**:
   - 如果没有空考位（kzwhxq为空数组），记录空考位消息
   - 如果没有编排考生，zkzh和bpzwh为空
   - 处理各种入场备注情况

3. **数据库操作**:
   - 向`ks_kkw_msg`表插入空考位消息
   - 根据条件更新`ks_ksrcxx`表的考生入场信息

### 3. 业务规则实现

#### 根据rcbz字段处理不同情况：

- **rcbz = 1,2,3时**: 处理ysbhyxx（已上报核验信息）和ysbrcxx（已上报入场信息）
  - 1: 误识别
  - 2: 坐错他人位置  
  - 3: 实际未参加考试

- **rcbz = 6时**: 缺考（空位），标记为空考位

- **其他rcbz值**: 按照相应业务规则处理

#### 数据优先级处理：
- **10**: 普通考场（入场上报）
- **20**: 考场空位上报
- **30**: 备用考场上报入场

### 4. 核验信息处理

当rcbz为1,2,3时，处理已上报核验信息：

#### 验证方式和结果映射：
- **验证方式**: 1-身份证验证, 2-人脸验证, 3-指纹验证
- **验证结果**: 1-通过, 0-不通过

#### 入场状态处理：
- **0**: 人工审核不通过
- **1**: 人工审核通过  
- **2**: 缺考

## 数据库表结构

### ks_kkw_msg（考试空考位消息）表字段：
- `id`: 唯一标识
- `ksjhbh`: 考试计划编号
- `ccm`: 场次码
- `bzhkdid`: 标准化考点ID
- `bzhkcid`: 标准化考场ID
- `sn`: 设备序列号
- `ks_zkzh`: 考生准考证号
- `ks_bpzwh`: 考生编排座位号
- `ks_sjzwh`: 考生实际座位号
- `ks_kkw`: 空考位标识
- `rcbz`: 入场备注
- `sjyxj`: 数据优先级
- 其他相关字段...

### ks_ksrcxx（考生入场信息）表字段：
- `ksrcbh`: 入场信息唯一编号
- `ksjhbh`: 考试计划编号
- `ccm`: 场次码
- `zkzh`: 准考证号
- `yzfs`: 验证方式
- `yzjg`: 验证结果
- `sfrc`: 是否入场
- `rcsj`: 入场时间
- `rgyzjg`: 人工验证结果
- `sbzt`: 上报状态
- 其他相关字段...

## 使用方式

### 1. MQTT消息处理
系统会自动接收MQTT消息，通过`MqttSbkkwEventHandler`处理`SBKKW`事件类型的消息。

### 2. 数据流程
1. 接收加密的MQTT消息
2. 解密数据获取业务信息
3. 解析空考位数据结构
4. 根据业务规则处理数据
5. 写入/更新数据库表

### 3. 异常处理
- 解密失败时记录错误日志
- 数据解析失败时跳过处理
- 数据库操作异常时回滚事务

## 测试

提供了完整的单元测试类`MqttSbkkwEventDbHandlerTest`，包含：

1. **有考生数据的测试**: 测试正常的空考位上报流程
2. **无考生数据的测试**: 测试kzwhxq为空数组的情况
3. **无编排考生的测试**: 测试zkzh和bpzwh为空的情况

## 注意事项

1. **数据加密**: 所有接收的数据都需要使用SM4算法解密
2. **事务处理**: 所有数据库操作都在事务中执行，确保数据一致性
3. **日志记录**: 关键操作都有详细的日志记录，便于问题排查
4. **异常处理**: 完善的异常处理机制，避免单个数据错误影响整体处理

## 扩展性

代码设计考虑了扩展性：
- 可以轻松添加新的rcbz类型处理
- 支持新的验证方式和结果类型
- 便于添加新的业务规则

## 依赖

- Spring Boot
- MyBatis
- FastJSON
- SM4加密工具类
- 日期工具类
- ID生成工具类
