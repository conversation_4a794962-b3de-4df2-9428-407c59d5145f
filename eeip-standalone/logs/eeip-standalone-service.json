{"@timestamp":"2025-07-30T03:37:55.528Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$67167b38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:37:58.189Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-30T03:37:58.196Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-30T03:38:08.414Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-30T03:38:08.528Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-30T03:38:09.973Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-30T03:38:10.253Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-30T03:38:10.651Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-30T03:38:13.684Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"当前数据库为[mysql]"}
{"@timestamp":"2025-07-30T03:38:22.914Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-30T03:38:26.174Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"com.xcwlkj.service.KsKkwMsgServiceTest","rest":"Started KsKkwMsgServiceTest in 32.36 seconds (JVM running for 33.786)"}
{"@timestamp":"2025-07-30T03:38:26.534Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-30T03:38:26.609Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：8"}
{"@timestamp":"2025-07-30T03:38:26.662Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：4，摘要信息：KkwTjInfoVO[total=5,ysb=4,wsb=1,reportRate=80.00%]"}
{"@timestamp":"2025-07-30T03:38:26.717Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：2"}
{"@timestamp":"2025-07-30T03:38:26.767Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：5，摘要信息：KkwTjInfoVO[total=5,ysb=4,wsb=1,reportRate=80.00%]"}
{"@timestamp":"2025-07-30T03:38:26.803Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11036","thread":"main","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：2"}
