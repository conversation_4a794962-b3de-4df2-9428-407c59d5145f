[2m2025-07-30 11:38:37.963[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88005af1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:38.490[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 11:38:40.728[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-30 11:38:40.729[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-30 11:38:40.732[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: aloneoha
[2m2025-07-30 11:38:47.163[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-30 11:38:47.170[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-30 11:38:47.484[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 283ms. Found 0 Redis repository interfaces.
[2m2025-07-30 11:38:47.696[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 11:38:47.964[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 11:38:48.616[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6
[2m2025-07-30 11:38:48.649[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-30 11:38:48.659[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-30 11:38:48.673[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-30 11:38:48.795[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$c8de378a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:48.797[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$82107cba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:48.901[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$61a9f019] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.413[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6be657f4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.490[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$47893571] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.550[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$83aa6717] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.610[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.668[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d6d81323] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:49.721[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88005af1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 11:38:50.518[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-30 11:38:50.741[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 9975 ms
[2m2025-07-30 11:38:55.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-30 11:38:55.603[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 11:38:55.747[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 11:38:55.917[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 11:38:55.930[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-30 11:38:55.930[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-30 11:38:55.932[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-30 11:38:55.932[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-30 11:38:57.599[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-30 11:39:01.845[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-30 11:39:01.846[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 11:39:04.588[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-30 11:39:04.637[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-30 11:39:17.742[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-30 11:39:17.748[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-30 11:39:20.687[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-30 11:39:20.986[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-30 11:39:21.896[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-30 11:39:21.967[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-30 11:39:22.033[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 11:39:22.036[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 未配置默认平台类型，跳过上级平台DFS客户端初始化
[2m2025-07-30 11:39:24.249[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-30 11:39:24.250[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-30 11:39:24.250[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:24.262[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 11:39:24.262[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-30 11:39:26.254[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-30 11:39:26.339[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 当前数据库为[mysql]
[2m2025-07-30 11:39:26.995[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5e73a0e4],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@129d425e],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@6ad5f700],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@3056bd1d],]
[2m2025-07-30 11:39:26.995[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-30 11:39:34.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-30 11:39:34.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-30 11:39:34.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-30 11:39:34.445[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-30 11:39:36.418[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-30 11:39:38.169[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 11:39:40.679[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-30 11:39:40.680[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 11:39:40.681[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 11:39:40.789[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 11:39:45.003[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-30 11:39:48.180[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-30 11:39:48.180[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-30 11:39:48.350[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-30 11:39:48.353[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-30 11:39:48.660[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 72.655 seconds (JVM running for 73.872)
[2m2025-07-30 11:39:48.735[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息
[2m2025-07-30 11:39:48.735[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-aloneoha.yml
[2m2025-07-30 11:39:48.741[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息【成功】
[2m2025-07-30 11:39:49.092[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-30 11:39:49.211[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-30 11:39:49.212[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-30 11:39:49.232[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:49.233[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-30 11:39:49.234[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:49.255[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:49.262[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-30 11:39:49.263[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-30 11:39:49.267[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-30 11:39:49.328[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-30 11:39:50.627[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-30 11:39:50.632[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 11:39:50.633[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 11:39:50.634[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 11:39:50.659[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-30 11:39:51.412[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-30 11:39:51.412[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-30 11:39:51.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-30 11:39:51.417[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-30 11:39:51.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 11:39:51.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-30 11:39:51.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-30 11:39:51.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 11:39:51.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-30 11:39:51.427[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-30 11:39:51.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-30 11:39:51.446[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-30 11:39:51.442(Timestamp), 1(Integer)
[2m2025-07-30 11:39:51.472[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.474[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-30 11:39:51.474[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-30 11:39:51.474[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone_oha]
[2m2025-07-30 11:39:51.477[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.477[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)
[2m2025-07-30 11:39:51.497[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.497[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.497[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-30 11:39:51.507[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.512[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.512[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-30 11:39:51.524[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.524[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.524[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-30 11:39:51.536[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.536[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.536[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-30 11:39:51.552[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.554[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.554[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-30 11:39:51.565[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.566[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.566[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-30 11:39:51.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)
[2m2025-07-30 11:39:51.587[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.587[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.587[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-30 11:39:51.599[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.606[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.606[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-30 11:39:51.616[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.616[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.616[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-30 11:39:51.633[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.634[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-30 11:39:51.646[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.646[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.648[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-30 11:39:51.659[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.661[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.661[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-30 11:39:51.670[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.670[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.674[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.698[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.699[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 11:39:51.699[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.724[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.725[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 11:39:51.725[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.751[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.752[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-30 11:39:51.752[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.777[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.795[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.795[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.821[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.823[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.823[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.851[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.852[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.853[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.881[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.884[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.884[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.907[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.907[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 11:39:51.907[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:51.937[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:51.951[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-30 11:39:51.951[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-30 11:39:51.962[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 11:39:51.962[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.962[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)
[2m2025-07-30 11:39:51.977[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.977[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.977[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-30 11:39:51.986[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.986[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.986[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-30 11:39:51.998[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:51.998[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:51.998[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-30 11:39:52.014[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-30 11:39:52.017[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 11:39:52.037[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:52.037[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.037[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-30 11:39:52.047[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.057[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-30 11:39:52.057[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-30 11:39:52.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 11:39:52.096[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-30 11:39:52.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.096[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-30 11:39:52.109[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.109[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.109[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-30 11:39:52.125[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.127[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.127[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-30 11:39:52.137[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.137[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.141[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)
[2m2025-07-30 11:39:52.147[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 等待来自服务器的响应时超时; retrying...
[2m2025-07-30 11:39:52.147[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.147[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 11:39:52.147[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)
[2m2025-07-30 11:39:52.160[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:39:52.160[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-30 11:39:52.160[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-30 11:39:52.160[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 11:39:53.902[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 11:39:53.987[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 85 ms
[2m2025-07-30 11:39:54.980[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 11:39:56.190[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-30 11:39:59.460[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[      Thread-84][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-30 11:39:59.460[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[      Thread-83][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-30 11:40:18.740[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 等待来自服务器的响应时超时; retrying...
[2m2025-07-30 11:40:38.257[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-30 11:40:38.258[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-30 11:40:38(String)
[2m2025-07-30 11:40:38.273[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-30 11:40:38.317[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.service.impl.AttachmentHandler    [0;39m [2m:[0;39m 将删除的文件key==>[2507291550101399781054444732416, 2507291553071399781798833029120, 2507291555281399782391072948224, 2507291708571399800881708269568]
[2m2025-07-30 11:40:38.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.deleteByExample               [0;39m [2m:[0;39m ==>  Preparing: DELETE FROM attachment WHERE ( ( path in ( ? , ? , ? , ? ) ) ) 
[2m2025-07-30 11:40:38.387[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.deleteByExample               [0;39m [2m:[0;39m ==> Parameters: 2507291550101399781054444732416(String), 2507291553071399781798833029120(String), 2507291555281399782391072948224(String), 2507291708571399800881708269568(String)
[2m2025-07-30 11:40:38.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.deleteByExample               [0;39m [2m:[0;39m <==    Updates: 4
[2m2025-07-30 11:40:52.345[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 已断开连接; retrying...
[2m2025-07-30 11:41:12.137[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 已断开连接; retrying...
[2m2025-07-30 11:41:39.196[0;39m [32m INFO [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-30 11:41:39.758[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count 
[2m2025-07-30 11:41:39.758[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 11:41:39.767[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:41:39.775[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? 
[2m2025-07-30 11:41:39.775[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 11:41:39.792[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:41:39.820[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count 
[2m2025-07-30 11:41:39.820[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 11:41:39.832[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:41:39.832[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? 
[2m2025-07-30 11:41:39.832[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 11:41:39.848[0;39m [32mDEBUG [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-30 11:41:39.877[0;39m [32m INFO [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.service.impl.KsKkwMsgServiceImpl  [0;39m [2m:[0;39m 空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]
[2m2025-07-30 11:41:39.877[0;39m [32m INFO [,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,false][0;39m [33m[eeip-standalone-service,f86a8b9d55fa1a7e,f86a8b9d55fa1a7e,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-4][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-30 11:41:52.776[0;39m [32m INFO [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-30 11:41:52.800[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx ON msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') 
[2m2025-07-30 11:41:52.800[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 101(String)
[2m2025-07-30 11:41:52.808[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 11:41:52.815[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m ==>  Preparing: SELECT kc.kcbh as kch, kc.ljkcbh as ljkcbh, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, msg.KS_ZKZH as zkzh, ksxx.ksxm as xm, msg.KS_BPZWH as zwh, msg.RCBZ yclx, DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj, msg.report_flag as sfsb, DATE_FORMAT(msg.report_time, '%Y-%m-%d %H:%i:%s') as sbsjsj FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx on msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') ORDER BY kc.ljkcbh, msg.KS_BPZWH LIMIT ? 
[2m2025-07-30 11:41:52.815[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 101(String), 10(Integer)
[2m2025-07-30 11:41:52.826[0;39m [32mDEBUG [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-30 11:41:52.833[0;39m [32m INFO [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.service.impl.KsKkwMsgServiceImpl  [0;39m [2m:[0;39m 空考位详情查询完成，总记录数：3
[2m2025-07-30 11:41:52.833[0;39m [32m INFO [,647f7c5b63514704,647f7c5b63514704,false][0;39m [33m[eeip-standalone-service,647f7c5b63514704,647f7c5b63514704,,false][0;39m [35m37948[0;39m [2m---[0;39m [2m[nio-8888-exec-6][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwXqRespModel[pageInfo=PageInfo{pageNum=1, pageSize=10, size=3, startRow=1, endRow=3, total=3, pages=1, list=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=3, pages=1, reasonable=true, pageSizeZero=false}[KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501003,xm=王五,zwh=03,yclx=6,zdsbsj=2025-07-29 09:00:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501002,xm=李四,zwh=03,yclx=4,zdsbsj=2025-07-29 09:06:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501001,xm=张三,zwh=04,yclx=7,zdsbsj=2025-07-29 09:01:00,sfsb=0,sbsjsj=<null>]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-30 11:42:11.164[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 已断开连接; retrying...
[2m2025-07-30 11:42:31.413[0;39m [31mERROR [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[670a7176193b696][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m Lost connection: 等待来自服务器的响应时超时; retrying...
[2m2025-07-30 11:42:44.279[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m37948[0;39m [2m---[0;39m [2m[     Thread-162][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
[2m2025-07-30 13:22:16.946[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$92227cb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:17.423[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 13:22:19.570[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-30 13:22:19.571[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-30 13:22:19.573[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: aloneoha
[2m2025-07-30 13:22:24.799[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-30 13:22:24.802[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-30 13:22:25.081[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 249ms. Found 0 Redis repository interfaces.
[2m2025-07-30 13:22:25.239[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 13:22:25.437[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 13:22:25.866[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6
[2m2025-07-30 13:22:25.889[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-30 13:22:25.898[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-30 13:22:25.911[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-30 13:22:25.998[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$d3005949] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:25.999[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$8c329e79] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.072[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$6bcc11d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.400[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$760879b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$51ab5730] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.500[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$8dcc88d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.528[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.571[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.579[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$e0fa34e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:26.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$92227cb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:22:27.247[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-30 13:22:27.384[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 7787 ms
[2m2025-07-30 13:22:30.619[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-30 13:22:30.731[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:22:30.822[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:22:30.921[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:22:30.927[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-30 13:22:30.927[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-30 13:22:30.927[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-30 13:22:30.927[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-30 13:22:31.901[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-30 13:22:34.138[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-30 13:22:34.139[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:22:35.729[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-30 13:22:35.742[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-30 13:22:44.356[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-30 13:22:44.364[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-30 13:22:46.014[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-30 13:22:46.125[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-30 13:22:46.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-30 13:22:46.435[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-30 13:22:46.459[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:22:46.461[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 未配置默认平台类型，跳过上级平台DFS客户端初始化
[2m2025-07-30 13:22:48.388[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-30 13:22:48.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-30 13:22:48.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:22:48.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:22:48.402[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-30 13:22:50.173[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-30 13:22:50.229[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 当前数据库为[mysql]
[2m2025-07-30 13:22:50.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@1d4a6871],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2186e29d],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@24abed19],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@2977512d],]
[2m2025-07-30 13:22:50.556[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-30 13:22:55.341[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-30 13:22:55.342[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-30 13:22:55.342[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-30 13:22:55.371[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-30 13:22:56.549[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-30 13:22:57.895[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 13:22:59.375[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-30 13:22:59.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-30 13:22:59.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-30 13:22:59.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-30 13:22:59.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-30 13:22:59.376[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-30 13:22:59.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-30 13:22:59.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-30 13:22:59.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-30 13:22:59.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 13:22:59.377[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 13:22:59.410[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 13:23:02.239[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-30 13:23:02.632[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-30 13:23:02.632[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-30 13:23:02.736[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-30 13:23:02.737[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-30 13:23:03.034[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 47.656 seconds (JVM running for 48.475)
[2m2025-07-30 13:23:03.062[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息
[2m2025-07-30 13:23:03.063[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-aloneoha.yml
[2m2025-07-30 13:23:03.066[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息【成功】
[2m2025-07-30 13:23:03.113[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-30 13:23:03.222[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-30 13:23:03.223[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-30 13:23:03.247[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:03.248[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-30 13:23:03.248[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:03.271[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:03.275[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-30 13:23:03.275[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-30 13:23:03.276[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-30 13:23:03.286[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-30 13:23:03.551[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:23:03.557[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:23:03.558[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:23:03.573[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-30 13:23:04.256[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-30 13:23:04.256[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-30 13:23:04.261[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-30 13:23:04.261[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-30 13:23:04.272[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:23:04.273[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-30 13:23:04.273[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-30 13:23:04.273[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:23:04.273[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-30 13:23:04.274[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-30 13:23:04.278[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-30 13:23:04.279[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-30 13:23:04.275(Timestamp), 1(Integer)
[2m2025-07-30 13:23:04.300[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.300[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-30 13:23:04.300[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-30 13:23:04.300[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone_oha]
[2m2025-07-30 13:23:04.302[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.302[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)
[2m2025-07-30 13:23:04.315[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.315[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.316[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-30 13:23:04.327[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.328[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.328[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-30 13:23:04.339[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.339[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.341[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-30 13:23:04.352[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.353[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.353[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-30 13:23:04.363[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.364[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.364[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-30 13:23:04.375[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.376[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-30 13:23:04.388[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.389[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)
[2m2025-07-30 13:23:04.401[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.402[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-30 13:23:04.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.413[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.414[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-30 13:23:04.425[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.427[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-30 13:23:04.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.438[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-30 13:23:04.450[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.451[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.451[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-30 13:23:04.462[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.463[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.464[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-30 13:23:04.474[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.475[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.475[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.500[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.501[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 13:23:04.501[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.525[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 13:23:04.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.551[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.552[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-30 13:23:04.552[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.575[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.577[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.577[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.600[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.601[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.602[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.625[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.648[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.649[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.649[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.671[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.672[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:23:04.673[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.698[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.701[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-30 13:23:04.702[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-30 13:23:04.715[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 13:23:04.716[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.716[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)
[2m2025-07-30 13:23:04.728[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.729[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.729[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-30 13:23:04.742[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.743[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.743[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-30 13:23:04.757[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.758[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.758[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-30 13:23:04.772[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.773[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-30 13:23:04.773[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:23:04.801[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.801[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.802[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-30 13:23:04.815[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.816[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-30 13:23:04.816[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-30 13:23:04.872[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:23:04.872[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-30 13:23:04.873[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.873[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-30 13:23:04.886[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.887[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.887[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-30 13:23:04.898[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.898[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.898[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-30 13:23:04.909[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.911[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.911[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)
[2m2025-07-30 13:23:04.922[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.923[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:23:04.923[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)
[2m2025-07-30 13:23:04.934[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:23:04.935[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-30 13:23:04.935[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-30 13:23:04.935[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:23:05.587[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 13:23:05.644[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 57 ms
[2m2025-07-30 13:23:06.401[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 13:23:06.642[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-30 13:23:08.521[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[      Thread-63][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-30 13:23:11.528[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[      Thread-64][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-30 13:23:52.681[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-30 13:23:52.681[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-30 13:23:52(String)
[2m2025-07-30 13:23:52.692[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:24:02.599[0;39m [32m INFO [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-30 13:24:02.709[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx ON msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' LEFT JOIN (SELECT sum(sbkszs) AS sbkszs, ksjhbh, ccm, ljkcbh, sblx, max(report_flag) AS report_flag, max(report_time) AS report_time FROM ks_ydsb_sbkszs GROUP BY ksjhbh, ccm, ljkcbh, sblx) sbzs ON msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') 
[2m2025-07-30 13:24:02.709[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 101(String)
[2m2025-07-30 13:24:02.723[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjXq_COUNT           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:24:02.725[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m ==>  Preparing: SELECT kc.kcbh as kch, kc.ljkcbh as ljkcbh, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, msg.KS_ZKZH as zkzh, ksxx.ksxm as xm, msg.KS_BPZWH as zwh, msg.RCBZ yclx, DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj, msg.report_flag as sfsb, DATE_FORMAT(msg.report_time, '%Y-%m-%d %H:%i:%s') as sbsjsj FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx on msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' LEFT JOIN ( select sum(sbkszs) as sbkszs,ksjhbh,ccm,ljkcbh,sblx,max(report_flag) as report_flag,max(report_time) as report_time from ks_ydsb_sbkszs GROUP BY ksjhbh,ccm,ljkcbh,sblx ) sbzs ON msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') ORDER BY kc.ljkcbh, msg.KS_BPZWH LIMIT ? 
[2m2025-07-30 13:24:02.726[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 101(String), 10(Integer)
[2m2025-07-30 13:24:02.743[0;39m [32mDEBUG [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjXq    [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-30 13:24:02.744[0;39m [32m INFO [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.service.impl.KsKkwMsgServiceImpl  [0;39m [2m:[0;39m 空考位详情查询完成，总记录数：3
[2m2025-07-30 13:24:02.744[0;39m [32m INFO [,e85aa801dd86b31b,e85aa801dd86b31b,false][0;39m [33m[eeip-standalone-service,e85aa801dd86b31b,e85aa801dd86b31b,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwXqRespModel[pageInfo=PageInfo{pageNum=1, pageSize=10, size=3, startRow=1, endRow=3, total=3, pages=1, list=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=3, pages=1, reasonable=true, pageSizeZero=false}[KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501003,xm=王五,zwh=03,yclx=6,zdsbsj=2025-07-29 09:00:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501002,xm=李四,zwh=03,yclx=4,zdsbsj=2025-07-29 09:06:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501001,xm=张三,zwh=04,yclx=7,zdsbsj=2025-07-29 09:01:00,sfsb=0,sbsjsj=<null>]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-30 13:24:17.386[0;39m [32m INFO [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-30 13:24:17.407[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count 
[2m2025-07-30 13:24:17.407[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 13:24:17.419[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:24:17.420[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? 
[2m2025-07-30 13:24:17.420[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 13:24:17.432[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:24:17.436[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count 
[2m2025-07-30 13:24:17.437[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 13:24:17.448[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:24:17.449[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? 
[2m2025-07-30 13:24:17.449[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 13:24:17.462[0;39m [32mDEBUG [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-30 13:24:17.463[0;39m [32m INFO [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.service.impl.KsKkwMsgServiceImpl  [0;39m [2m:[0;39m 空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]
[2m2025-07-30 13:24:17.463[0;39m [32m INFO [,67a07ebb92d42315,67a07ebb92d42315,false][0;39m [33m[eeip-standalone-service,67a07ebb92d42315,67a07ebb92d42315,,false][0;39m [35m30040[0;39m [2m---[0;39m [2m[nio-8888-exec-9][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-30 13:24:59.831[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m30040[0;39m [2m---[0;39m [2m[     Thread-118][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
[2m2025-07-30 13:25:11.621[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8184fc8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:12.182[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 13:25:14.399[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
[2m2025-07-30 13:25:14.400[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
[2m2025-07-30 13:25:14.402[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m The following profiles are active: aloneoha
[2m2025-07-30 13:25:20.187[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode!
[2m2025-07-30 13:25:20.194[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-30 13:25:20.504[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 283ms. Found 0 Redis repository interfaces.
[2m2025-07-30 13:25:20.719[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 13:25:20.978[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.boot.actuate.endpoint.EndpointId    [0;39m [2m:[0;39m Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
[2m2025-07-30 13:25:21.626[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.cloud.context.scope.GenericScope    [0;39m [2m:[0;39m BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6
[2m2025-07-30 13:25:21.659[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
[2m2025-07-30 13:25:21.671[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
[2m2025-07-30 13:25:21.687[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mfaultConfiguringBeanFactoryPostProcessor[0;39m [2m:[0;39m No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
[2m2025-07-30 13:25:21.812[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$c262d927] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:21.814[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$7b951e57] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:21.928[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$5b2e91b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.367[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$656af991] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.449[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$410dd70e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.506[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$7d2f08b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.552[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.601[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.613[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d05cb4c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:22.658[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mtrationDelegate$BeanPostProcessorChecker[0;39m [2m:[0;39m Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8184fc8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[2m2025-07-30 13:25:23.430[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port(s): 8888 (http)
[2m2025-07-30 13:25:23.669[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.web.context.ContextLoader           [0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 9235 ms
[2m2025-07-30 13:25:27.518[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.ServletEndpointRegistrar  [0;39m [2m:[0;39m Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
[2m2025-07-30 13:25:27.632[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:25:27.804[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:25:27.872[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:25:27.877[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsServerUrl=http://************:8811
[2m2025-07-30 13:25:27.877[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsChannel=ZJKSZHPT
[2m2025-07-30 13:25:27.877[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
[2m2025-07-30 13:25:27.877[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XcDfsConfig  [0;39m [2m:[0;39m ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
[2m2025-07-30 13:25:29.274[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.interceptor.SqlLogInterceptor  [0;39m [2m:[0;39m [打印SQL拦截器创建]noticeTime=5.0秒
[2m2025-07-30 13:25:32.518[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.c.AsyncTaskExecutorConfiguration  [0;39m [2m:[0;39m Creating Async Task Executor
[2m2025-07-30 13:25:32.521[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:25:34.667[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m SMS Bean IAcsClient Start
[2m2025-07-30 13:25:34.703[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.pubc.config.SmsConfig        [0;39m [2m:[0;39m 加载SMS Bean IAcsClient OK
[2m2025-07-30 13:25:45.220[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化####
[2m2025-07-30 13:25:45.228[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.identityverify.config.XcDfsConfig   [0;39m [2m:[0;39m ####文件系统初始化成功####
[2m2025-07-30 13:25:47.163[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.taskcenter.DefaultHandleFactory   [0;39m [2m:[0;39m 配置线程池工作线程数量[16]
[2m2025-07-30 13:25:47.501[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 开始检查平台类型和初始化上级平台DFS客户端...
[2m2025-07-30 13:25:48.153[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
[2m2025-07-30 13:25:48.208[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m ==> Parameters: defaultPlat(String)
[2m2025-07-30 13:25:48.251[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.J.selectOneByExample            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:25:48.254[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.handler.UpperHsDfsHandler         [0;39m [2m:[0;39m 未配置默认平台类型，跳过上级平台DFS客户端初始化
[2m2025-07-30 13:25:50.929[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动中....
[2m2025-07-30 13:25:50.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
[2m2025-07-30 13:25:50.931[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:25:50.943[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.ScheduleJobMapper.selectAll     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:25:50.944[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.s.impl.ScheduleJobServiceImpl     [0;39m [2m:[0;39m ###定时任务初始化启动完成
[2m2025-07-30 13:25:53.253[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.c.ThreadPoolTaskScheduler         [0;39m [2m:[0;39m Initializing ExecutorService 'taskScheduler'
[2m2025-07-30 13:25:53.363[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 当前数据库为[mysql]
[2m2025-07-30 13:25:54.293[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@423287bb],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@4a5dc3c9],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@7dd96f87],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@6d91fbe7],]
[2m2025-07-30 13:25:54.293[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.FeignHystrixConcurrencyStrategyIntellif[0;39m [2m:[0;39m Registering Sleuth Hystrix Concurrency Strategy.
[2m2025-07-30 13:26:03.071[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
[2m2025-07-30 13:26:03.071[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
[2m2025-07-30 13:26:03.071[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.standalone.config.MqttConfig [0;39m [2m:[0;39m ######监听MQTT主题[/+/+/event/+]
[2m2025-07-30 13:26:03.126[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.xcwlkj.standalone.config.XxlJobConfig [0;39m [2m:[0;39m >>>>>>>>>>> xxl-job config init.
[2m2025-07-30 13:26:05.138[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.f.FreeMarkerAutoConfiguration   [0;39m [2m:[0;39m Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
[2m2025-07-30 13:26:06.894[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 21 endpoint(s) beneath base path '/actuator'
[2m2025-07-30 13:26:09.444[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
[2m2025-07-30 13:26:09.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.channel.PublishSubscribeChannel   [0;39m [2m:[0;39m Channel 'application-1.errorChannel' has 1 subscriber(s).
[2m2025-07-30 13:26:09.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started _org.springframework.integration.errorLogger
[2m2025-07-30 13:26:09.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
[2m2025-07-30 13:26:09.446[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.mqttOutbound.serviceActivator
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.integration.channel.ExecutorChannel [0;39m [2m:[0;39m Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.endpoint.EventDrivenConsumer      [0;39m [2m:[0;39m started mqttConfig.handler.serviceActivator
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mProxyFactoryBean$MethodInvocationGateway[0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 13:26:09.447[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.i.gateway.GatewayProxyFactoryBean   [0;39m [2m:[0;39m started mqttGateway
[2m2025-07-30 13:26:09.528[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.eureka.InstanceInfoFactory      [0;39m [2m:[0;39m Setting initial instance status as: STARTING
[2m2025-07-30 13:26:13.710[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
[2m2025-07-30 13:26:15.277[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.m.i.MqttPahoMessageDrivenChannelAdapter[0;39m [2m:[0;39m started inbound
[2m2025-07-30 13:26:15.277[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.quartz.SchedulerFactoryBean       [0;39m [2m:[0;39m Starting Quartz Scheduler now
[2m2025-07-30 13:26:15.471[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port(s): 8888 (http) with context path ''
[2m2025-07-30 13:26:15.473[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.c.n.e.s.EurekaAutoServiceRegistration[0;39m [2m:[0;39m Updating port to 8888
[2m2025-07-30 13:26:15.785[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.standalone.StandaloneApplication    [0;39m [2m:[0;39m Started StandaloneApplication in 66.069 seconds (JVM running for 67.324)
[2m2025-07-30 13:26:15.864[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息
[2m2025-07-30 13:26:15.865[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-aloneoha.yml
[2m2025-07-30 13:26:15.870[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.util.YmlUtil                 [0;39m [2m:[0;39m 开始从application-aloneoha.yml加载配置信息【成功】
[2m2025-07-30 13:26:16.239[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.cache.AbstractRedisDataCache [0;39m [2m:[0;39m [PkgDataFileServices]cleanup cache finished
[2m2025-07-30 13:26:16.360[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
[2m2025-07-30 13:26:16.361[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
[2m2025-07-30 13:26:16.381[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:16.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
[2m2025-07-30 13:26:16.382[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:16.404[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.clearDoing                    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:16.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
[2m2025-07-30 13:26:16.415[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
[2m2025-07-30 13:26:16.419[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
[2m2025-07-30 13:26:16.473[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.c.z.r.z.ZookeeperRegistryCenter     [0;39m [2m:[0;39m Elastic job: zookeeper registry center init, server lists is: **************:2181.
[2m2025-07-30 13:26:17.448[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####开始缓存商户配置信息####
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.b.c.BasicinfoBusiCacheInitConfig    [0;39m [2m:[0;39m ####缓存商户配置信息缓存成功####
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####开始缓存配置信息####
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####缓存配置信息缓存成功####
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 13:26:17.453[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.xcwlkj.biz.config.BizInitConfig     [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####开始缓存公共服务配置信息####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.pubc.config.PubcBusiCacheInitConfig [0;39m [2m:[0;39m ####缓存公共服务配置信息缓存成功####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####开始缓存听评课配置信息####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####缓存听评课配置信息缓存成功####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.e.c.EvaluationBusiCacheInitConfig   [0;39m [2m:[0;39m ####消息队列初始化成功####
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:26:17.454[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Init##
[2m2025-07-30 13:26:17.477[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####开始缓存校级身份核验平台配置信息####
[2m2025-07-30 13:26:18.241[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
[2m2025-07-30 13:26:18.241[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化####
[2m2025-07-30 13:26:18.250[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
[2m2025-07-30 13:26:18.250[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m ==> Parameters: 1(String)
[2m2025-07-30 13:26:18.261[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.MbPzbMapper.selectByExample     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:26:18.262[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####码表数据缓存初始化成功####
[2m2025-07-30 13:26:18.262[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化####
[2m2025-07-30 13:26:18.262[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.s.concurrent.ThreadPoolTaskExecutor [0;39m [2m:[0;39m Initializing ExecutorService
[2m2025-07-30 13:26:18.263[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####延迟队列初始化成功####
[2m2025-07-30 13:26:18.263[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化####
[2m2025-07-30 13:26:18.273[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
[2m2025-07-30 13:26:18.275[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 2025-07-30 13:26:18.269(Timestamp), 1(Integer)
[2m2025-07-30 13:26:18.298[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.K.updateByExampleSelective      [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.298[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据下发任务初始化成功####
[2m2025-07-30 13:26:18.298[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化####
[2m2025-07-30 13:26:18.298[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m dbName:[eeip_alone_oha]
[2m2025-07-30 13:26:18.301[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.301[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)
[2m2025-07-30 13:26:18.319[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.319[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.320[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)
[2m2025-07-30 13:26:18.331[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.332[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)
[2m2025-07-30 13:26:18.343[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.344[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.344[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)
[2m2025-07-30 13:26:18.357[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.358[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.359[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)
[2m2025-07-30 13:26:18.371[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.371[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.371[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)
[2m2025-07-30 13:26:18.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.383[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.384[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)
[2m2025-07-30 13:26:18.395[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.396[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.397[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)
[2m2025-07-30 13:26:18.409[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.419[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)
[2m2025-07-30 13:26:18.433[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.434[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.435[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)
[2m2025-07-30 13:26:18.447[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.448[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.449[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)
[2m2025-07-30 13:26:18.460[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.461[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.461[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)
[2m2025-07-30 13:26:18.472[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.473[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.473[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)
[2m2025-07-30 13:26:18.485[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.486[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.487[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)
[2m2025-07-30 13:26:18.498[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.499[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.499[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.525[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createCsDzgh   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 13:26:18.526[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.549[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbIpdfp  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.550[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
[2m2025-07-30 13:26:18.550[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.575[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbSbjcb  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
[2m2025-07-30 13:26:18.576[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.600[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsBmxxKstzz   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.602[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.602[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createKsYdsbSbkszs  [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.626[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.651[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizCsxxExt    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.651[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.652[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.675[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTask   [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.676[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.676[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.701[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.createBizFsgjTaskJg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.702[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
[2m2025-07-30 13:26:18.703[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.727[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createKsKkwMsg [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.741[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
[2m2025-07-30 13:26:18.741[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)
[2m2025-07-30 13:26:18.753[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.filterTables   [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 13:26:18.754[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.755[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)
[2m2025-07-30 13:26:18.765[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.767[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.768[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)
[2m2025-07-30 13:26:18.779[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.780[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.780[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)
[2m2025-07-30 13:26:18.792[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.793[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.794[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)
[2m2025-07-30 13:26:18.807[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.808[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
[2m2025-07-30 13:26:18.808[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 13:26:18.833[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.createSbsj     [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.834[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.834[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)
[2m2025-07-30 13:26:18.845[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.855[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
[2m2025-07-30 13:26:18.855[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m ==> Parameters: 同步状态(String)
[2m2025-07-30 13:26:18.926[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.mapper.UtilsMapper.alterColumn    [0;39m [2m:[0;39m <==    Updates: 0
[2m2025-07-30 13:26:18.926[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.service.impl.UtilsServiceImpl     [0;39m [2m:[0;39m 数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更
[2m2025-07-30 13:26:18.927[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.927[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)
[2m2025-07-30 13:26:18.937[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.938[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.938[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)
[2m2025-07-30 13:26:18.950[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.950[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.951[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)
[2m2025-07-30 13:26:18.961[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.962[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.962[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)
[2m2025-07-30 13:26:18.973[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.974[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
[2m2025-07-30 13:26:18.974[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m ==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)
[2m2025-07-30 13:26:18.985[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.i.m.UtilsMapper.findColumnExist     [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:18.985[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####数据库变更初始化成功####
[2m2025-07-30 13:26:18.985[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36m.x.i.c.IdentityverifyBusiCacheInitConfig[0;39m [2m:[0;39m ####缓存校级身份核验平台配置信息缓存成功####
[2m2025-07-30 13:26:18.985[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.x.core.config.ZookeeperInitRunner     [0;39m [2m:[0;39m ###SystemCache Succ<<<<<<<<<<<<<##
[2m2025-07-30 13:26:19.681[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-07-30 13:26:19.954[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[-**************][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 272 ms
[2m2025-07-30 13:26:20.938[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Fetching config from server at : http://localhost:8888
[2m2025-07-30 13:26:21.826[0;39m [33m WARN [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[-**************][0;39m [36mc.c.c.ConfigServicePropertySourceLocator[0;39m [2m:[0;39m Could not locate PropertySource: label not found
[2m2025-07-30 13:26:26.491[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[      Thread-72][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_ATTENDANCE_JTXX,启动成功！
[2m2025-07-30 13:26:26.489[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[      Thread-73][0;39m [36mc.xcwlkj.msgque.que.XcRocektMqConsumer  [0;39m [2m:[0;39m 监听：JKYT_RSTJ_XXTS,启动成功！
[2m2025-07-30 13:26:37.452[0;39m [32m INFO [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]
[2m2025-07-30 13:26:37.879[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count 
[2m2025-07-30 13:26:37.879[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 13:26:37.891[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjInfo_COUNT         [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:37.893[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? 
[2m2025-07-30 13:26:37.893[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 13:26:37.909[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjInfo  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:37.927[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count 
[2m2025-07-30 13:26:37.928[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String)
[2m2025-07-30 13:26:37.939[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.K.selectKkwTjLb_COUNT           [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 13:26:37.939[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? 
[2m2025-07-30 13:26:37.941[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m ==> Parameters: TEST2025001(String), 100(Integer)
[2m2025-07-30 13:26:37.953[0;39m [32mDEBUG [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.m.KsKkwMsgMapper.selectKkwTjLb    [0;39m [2m:[0;39m <==      Total: 8
[2m2025-07-30 13:26:37.974[0;39m [32m INFO [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.service.impl.KsKkwMsgServiceImpl  [0;39m [2m:[0;39m 空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]
[2m2025-07-30 13:26:37.975[0;39m [32m INFO [,3875c5f538c9d20e,3875c5f538c9d20e,false][0;39m [33m[eeip-standalone-service,3875c5f538c9d20e,3875c5f538c9d20e,,false][0;39m [35m22240[0;39m [2m---[0;39m [2m[nio-8888-exec-2][0;39m [36mc.x.i.facade.manager.CxtjController     [0;39m [2m:[0;39m 处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]
[2m2025-07-30 13:27:05.362[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) 
[2m2025-07-30 13:27:05.363[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m ==> Parameters: 2025-07-30 13:27:05(String)
[2m2025-07-30 13:27:05.374[0;39m [32mDEBUG [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[duleTask-pool-0][0;39m [36mc.x.i.m.A.selectByExample               [0;39m [2m:[0;39m <==      Total: 0
[2m2025-07-30 13:27:23.903[0;39m [32m INFO [,,,][0;39m [33m[eeip-standalone-service,,,,][0;39m [35m22240[0;39m [2m---[0;39m [2m[     Thread-150][0;39m [36mo.s.c.n.e.s.EurekaServiceRegistry       [0;39m [2m:[0;39m Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
