{"@timestamp":"2025-07-30T03:38:37.963Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88005af1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:38.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T03:38:40.728Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-30T03:38:40.729Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-30T03:38:40.732Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: aloneoha"}
{"@timestamp":"2025-07-30T03:38:47.163Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-30T03:38:47.170Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-30T03:38:47.484Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 283ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-30T03:38:47.696Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T03:38:47.964Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T03:38:48.616Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6"}
{"@timestamp":"2025-07-30T03:38:48.649Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-30T03:38:48.659Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-30T03:38:48.673Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-30T03:38:48.795Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$c8de378a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:48.797Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$82107cba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:48.901Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$61a9f019] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$6be657f4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$47893571] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.550Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$83aa6717] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.610Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.668Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d6d81323] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:49.721Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$88005af1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T03:38:50.518Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-30T03:38:50.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 9975 ms"}
{"@timestamp":"2025-07-30T03:38:55.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-30T03:38:55.603Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T03:38:55.747Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T03:38:55.917Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T03:38:55.930Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-30T03:38:55.930Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-30T03:38:55.932Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-30T03:38:55.932Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-30T03:38:57.599Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-30T03:39:01.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-30T03:39:01.846Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T03:39:04.588Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-30T03:39:04.637Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-30T03:39:17.742Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-30T03:39:17.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-30T03:39:20.687Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-30T03:39:20.986Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-30T03:39:21.896Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-30T03:39:21.967Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-30T03:39:22.033Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T03:39:22.036Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-30T03:39:24.249Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-30T03:39:24.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-30T03:39:24.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:24.262Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T03:39:24.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-30T03:39:26.254Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-30T03:39:26.339Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"当前数据库为[mysql]"}
{"@timestamp":"2025-07-30T03:39:26.995Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5e73a0e4],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@129d425e],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@6ad5f700],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@3056bd1d],]"}
{"@timestamp":"2025-07-30T03:39:26.995Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-30T03:39:34.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-30T03:39:34.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-30T03:39:34.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-30T03:39:34.445Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-30T03:39:36.418Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-30T03:39:38.169Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-30T03:39:40.679Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-30T03:39:40.680Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T03:39:40.681Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T03:39:40.789Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-30T03:39:45.003Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-30T03:39:48.180Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-30T03:39:48.180Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-30T03:39:48.350Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-30T03:39:48.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-30T03:39:48.660Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 72.655 seconds (JVM running for 73.872)"}
{"@timestamp":"2025-07-30T03:39:48.735Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息"}
{"@timestamp":"2025-07-30T03:39:48.735Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-aloneoha.yml"}
{"@timestamp":"2025-07-30T03:39:48.741Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-30T03:39:49.092Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-30T03:39:49.211Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-30T03:39:49.212Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-30T03:39:49.232Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:49.233Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-30T03:39:49.234Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:49.255Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:49.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-30T03:39:49.263Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T03:39:49.267Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-30T03:39:49.328Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-30T03:39:50.627Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T03:39:50.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T03:39:50.633Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T03:39:50.634Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T03:39:50.659Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-30T03:39:51.412Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-30T03:39:51.412Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-30T03:39:51.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-30T03:39:51.417Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-30T03:39:51.427Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-30T03:39:51.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-30T03:39:51.446Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-30 11:39:51.442(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-30T03:39:51.472Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.474Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-30T03:39:51.474Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-30T03:39:51.474Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone_oha]"}
{"@timestamp":"2025-07-30T03:39:51.477Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.477Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-30T03:39:51.497Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.497Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.497Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-30T03:39:51.507Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.512Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.512Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-30T03:39:51.524Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.524Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.524Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-30T03:39:51.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-30T03:39:51.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.554Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.554Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-30T03:39:51.565Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.566Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.566Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-30T03:39:51.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-30T03:39:51.587Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.587Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.587Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-30T03:39:51.599Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.606Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.606Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T03:39:51.616Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.616Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.616Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-30T03:39:51.633Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.634Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-30T03:39:51.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.648Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T03:39:51.659Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.661Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.661Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-30T03:39:51.670Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.670Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.674Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.698Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.699Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T03:39:51.699Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.724Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.725Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T03:39:51.725Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.751Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.752Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-30T03:39:51.752Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.777Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.795Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.795Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.821Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.823Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.823Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.851Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.852Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.853Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.881Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.884Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.884Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.907Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.907Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T03:39:51.907Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:51.937Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:51.951Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-30T03:39:51.951Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-30T03:39:51.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-30T03:39:51.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-30T03:39:51.977Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.977Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.977Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-30T03:39:51.986Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.986Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.986Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-30T03:39:51.998Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:51.998Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:51.998Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-30T03:39:52.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-30T03:39:52.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T03:39:52.037Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:52.037Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.037Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T03:39:52.047Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.057Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-30T03:39:52.057Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-30T03:39:52.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T03:39:52.096Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-30T03:39:52.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T03:39:52.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.109Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-30T03:39:52.125Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-30T03:39:52.137Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.137Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.141Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)"}
{"@timestamp":"2025-07-30T03:39:52.147Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Ping: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 等待来自服务器的响应时超时; retrying..."}
{"@timestamp":"2025-07-30T03:39:52.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T03:39:52.147Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)"}
{"@timestamp":"2025-07-30T03:39:52.160Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:39:52.160Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-30T03:39:52.160Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-30T03:39:52.160Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T03:39:53.902Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"RMI TCP Connection(20)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-30T03:39:53.987Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"RMI TCP Connection(20)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 85 ms"}
{"@timestamp":"2025-07-30T03:39:54.980Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"RMI TCP Connection(19)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T03:39:56.190Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"RMI TCP Connection(19)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-30T03:39:59.460Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"Thread-84","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-30T03:39:59.460Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"Thread-83","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-30T03:40:18.740Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Ping: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 等待来自服务器的响应时超时; retrying..."}
{"@timestamp":"2025-07-30T03:40:38.257Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-30T03:40:38.258Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-30 11:40:38(String)"}
{"@timestamp":"2025-07-30T03:40:38.273Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-30T03:40:38.317Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.service.impl.AttachmentHandler","rest":"将删除的文件key==>[2507291550101399781054444732416, 2507291553071399781798833029120, 2507291555281399782391072948224, 2507291708571399800881708269568]"}
{"@timestamp":"2025-07-30T03:40:38.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.deleteByExample","rest":"==>  Preparing: DELETE FROM attachment WHERE ( ( path in ( ? , ? , ? , ? ) ) ) "}
{"@timestamp":"2025-07-30T03:40:38.387Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.deleteByExample","rest":"==> Parameters: 2507291550101399781054444732416(String), 2507291553071399781798833029120(String), 2507291555281399782391072948224(String), 2507291708571399800881708269568(String)"}
{"@timestamp":"2025-07-30T03:40:38.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.deleteByExample","rest":"<==    Updates: 4"}
{"@timestamp":"2025-07-30T03:40:52.345Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Rec: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 已断开连接; retrying..."}
{"@timestamp":"2025-07-30T03:41:12.137Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Rec: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 已断开连接; retrying..."}
{"@timestamp":"2025-07-30T03:41:39.196Z","severity":"INFO","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-30T03:41:39.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count "}
{"@timestamp":"2025-07-30T03:41:39.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T03:41:39.767Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:41:39.775Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? "}
{"@timestamp":"2025-07-30T03:41:39.775Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T03:41:39.792Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:41:39.820Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count "}
{"@timestamp":"2025-07-30T03:41:39.820Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T03:41:39.832Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:41:39.832Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? "}
{"@timestamp":"2025-07-30T03:41:39.832Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T03:41:39.848Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-30T03:41:39.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-30T03:41:39.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"f86a8b9d55fa1a7e","span":"f86a8b9d55fa1a7e","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-4","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-30T03:41:52.776Z","severity":"INFO","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-30T03:41:52.800Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx ON msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') "}
{"@timestamp":"2025-07-30T03:41:52.800Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"==> Parameters: TEST2025001(String), 101(String)"}
{"@timestamp":"2025-07-30T03:41:52.808Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T03:41:52.815Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"==>  Preparing: SELECT kc.kcbh as kch, kc.ljkcbh as ljkcbh, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, msg.KS_ZKZH as zkzh, ksxx.ksxm as xm, msg.KS_BPZWH as zwh, msg.RCBZ yclx, DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj, msg.report_flag as sfsb, DATE_FORMAT(msg.report_time, '%Y-%m-%d %H:%i:%s') as sbsjsj FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx on msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') ORDER BY kc.ljkcbh, msg.KS_BPZWH LIMIT ? "}
{"@timestamp":"2025-07-30T03:41:52.815Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"==> Parameters: TEST2025001(String), 101(String), 10(Integer)"}
{"@timestamp":"2025-07-30T03:41:52.826Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-30T03:41:52.833Z","severity":"INFO","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：3"}
{"@timestamp":"2025-07-30T03:41:52.833Z","severity":"INFO","service":"eeip-standalone-service","trace":"647f7c5b63514704","span":"647f7c5b63514704","parent":"","exportable":"false","pid":"37948","thread":"http-nio-8888-exec-6","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwXqRespModel[pageInfo=PageInfo{pageNum=1, pageSize=10, size=3, startRow=1, endRow=3, total=3, pages=1, list=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=3, pages=1, reasonable=true, pageSizeZero=false}[KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501003,xm=王五,zwh=03,yclx=6,zdsbsj=2025-07-29 09:00:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501002,xm=李四,zwh=03,yclx=4,zdsbsj=2025-07-29 09:06:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501001,xm=张三,zwh=04,yclx=7,zdsbsj=2025-07-29 09:01:00,sfsb=0,sbsjsj=<null>]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-30T03:42:11.164Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Rec: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 已断开连接; retrying..."}
{"@timestamp":"2025-07-30T03:42:31.413Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"MQTT Ping: identityVerify_test01_CENCGW100_SIV1c9f717de4acfc99_inbound_12bc67035259465fb670a7176193b696","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"Lost connection: 等待来自服务器的响应时超时; retrying..."}
{"@timestamp":"2025-07-30T03:42:44.279Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"37948","thread":"Thread-162","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-30T05:22:16.946Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$92227cb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:17.423Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T05:22:19.570Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-30T05:22:19.571Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-30T05:22:19.573Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: aloneoha"}
{"@timestamp":"2025-07-30T05:22:24.799Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-30T05:22:24.802Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-30T05:22:25.081Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 249ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-30T05:22:25.239Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T05:22:25.437Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T05:22:25.866Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6"}
{"@timestamp":"2025-07-30T05:22:25.889Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-30T05:22:25.898Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-30T05:22:25.911Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-30T05:22:25.998Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$d3005949] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:25.999Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$8c329e79] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.072Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$6bcc11d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.400Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$760879b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$51ab5730] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.500Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$8dcc88d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.528Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$e0fa34e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:26.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$92227cb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:22:27.247Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-30T05:22:27.384Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 7787 ms"}
{"@timestamp":"2025-07-30T05:22:30.619Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-30T05:22:30.731Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:22:30.822Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:22:30.921Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:22:30.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-30T05:22:30.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-30T05:22:30.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-30T05:22:30.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-30T05:22:31.901Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-30T05:22:34.138Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-30T05:22:34.139Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:22:35.729Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-30T05:22:35.742Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-30T05:22:44.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-30T05:22:44.364Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-30T05:22:46.014Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-30T05:22:46.125Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-30T05:22:46.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-30T05:22:46.435Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-30T05:22:46.459Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:22:46.461Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-30T05:22:48.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-30T05:22:48.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-30T05:22:48.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:22:48.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:22:48.402Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-30T05:22:50.173Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-30T05:22:50.229Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"当前数据库为[mysql]"}
{"@timestamp":"2025-07-30T05:22:50.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@1d4a6871],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2186e29d],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@24abed19],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@2977512d],]"}
{"@timestamp":"2025-07-30T05:22:50.556Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-30T05:22:55.341Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-30T05:22:55.342Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-30T05:22:55.342Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-30T05:22:55.371Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-30T05:22:56.549Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-30T05:22:57.895Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-30T05:22:59.375Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-30T05:22:59.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:22:59.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-30T05:22:59.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-30T05:22:59.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:22:59.376Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-30T05:22:59.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-30T05:22:59.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:22:59.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-30T05:22:59.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T05:22:59.377Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T05:22:59.410Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-30T05:23:02.239Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-30T05:23:02.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-30T05:23:02.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-30T05:23:02.736Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-30T05:23:02.737Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-30T05:23:03.034Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 47.656 seconds (JVM running for 48.475)"}
{"@timestamp":"2025-07-30T05:23:03.062Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息"}
{"@timestamp":"2025-07-30T05:23:03.063Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-aloneoha.yml"}
{"@timestamp":"2025-07-30T05:23:03.066Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-30T05:23:03.113Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-30T05:23:03.222Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-30T05:23:03.223Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-30T05:23:03.247Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:03.248Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-30T05:23:03.248Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:03.271Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:03.275Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-30T05:23:03.275Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T05:23:03.276Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-30T05:23:03.286Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-30T05:23:03.551Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:23:03.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:23:03.558Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:23:03.573Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-30T05:23:04.256Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-30T05:23:04.256Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-30T05:23:04.261Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-30T05:23:04.261Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-30T05:23:04.272Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:23:04.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-30T05:23:04.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-30T05:23:04.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:23:04.273Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-30T05:23:04.274Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-30T05:23:04.278Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-30T05:23:04.279Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-30 13:23:04.275(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-30T05:23:04.300Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.300Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-30T05:23:04.300Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-30T05:23:04.300Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone_oha]"}
{"@timestamp":"2025-07-30T05:23:04.302Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.302Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-30T05:23:04.315Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.315Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-30T05:23:04.327Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.328Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.328Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-30T05:23:04.339Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.339Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.341Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-30T05:23:04.352Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-30T05:23:04.363Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-30T05:23:04.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.376Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-30T05:23:04.388Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-30T05:23:04.401Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.402Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-30T05:23:04.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T05:23:04.425Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.427Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-30T05:23:04.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.438Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-30T05:23:04.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.451Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.451Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T05:23:04.462Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.463Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.464Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-30T05:23:04.474Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.475Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.475Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.500Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.501Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T05:23:04.501Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.525Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T05:23:04.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.551Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-30T05:23:04.552Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.575Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.577Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.577Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.600Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.601Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.625Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.648Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.649Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.649Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.671Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.672Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:23:04.673Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.698Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.701Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-30T05:23:04.702Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-30T05:23:04.715Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-30T05:23:04.716Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.716Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-30T05:23:04.728Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.729Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.729Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-30T05:23:04.742Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.743Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.743Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-30T05:23:04.757Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.758Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-30T05:23:04.772Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.773Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-30T05:23:04.773Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:23:04.801Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.801Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.802Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T05:23:04.815Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.816Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-30T05:23:04.816Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-30T05:23:04.872Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:23:04.872Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-30T05:23:04.873Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.873Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T05:23:04.886Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.887Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.887Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-30T05:23:04.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-30T05:23:04.909Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.911Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.911Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)"}
{"@timestamp":"2025-07-30T05:23:04.922Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.923Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:23:04.923Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)"}
{"@timestamp":"2025-07-30T05:23:04.934Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:23:04.935Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-30T05:23:04.935Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:23:04.935Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:23:05.587Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"RMI TCP Connection(1)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-30T05:23:05.644Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"RMI TCP Connection(1)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 57 ms"}
{"@timestamp":"2025-07-30T05:23:06.401Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"RMI TCP Connection(3)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T05:23:06.642Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"RMI TCP Connection(3)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-30T05:23:08.521Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"Thread-63","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-30T05:23:11.528Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"Thread-64","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-30T05:23:52.681Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-30T05:23:52.681Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-30 13:23:52(String)"}
{"@timestamp":"2025-07-30T05:23:52.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:24:02.599Z","severity":"INFO","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-30T05:24:02.709Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"==>  Preparing: SELECT count(0) FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx ON msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' LEFT JOIN (SELECT sum(sbkszs) AS sbkszs, ksjhbh, ccm, ljkcbh, sblx, max(report_flag) AS report_flag, max(report_time) AS report_time FROM ks_ydsb_sbkszs GROUP BY ksjhbh, ccm, ljkcbh, sblx) sbzs ON msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') "}
{"@timestamp":"2025-07-30T05:24:02.709Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"==> Parameters: TEST2025001(String), 101(String)"}
{"@timestamp":"2025-07-30T05:24:02.723Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjXq_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:24:02.725Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"==>  Preparing: SELECT kc.kcbh as kch, kc.ljkcbh as ljkcbh, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, msg.KS_ZKZH as zkzh, ksxx.ksxm as xm, msg.KS_BPZWH as zwh, msg.RCBZ yclx, DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj, msg.report_flag as sfsb, DATE_FORMAT(msg.report_time, '%Y-%m-%d %H:%i:%s') as sbsjsj FROM ks_kcxx kc INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.ccm LEFT JOIN ks_ksrcxx ksxx on msg.ksjhbh = ksxx.ksjhbh AND msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' LEFT JOIN ( select sum(sbkszs) as sbkszs,ksjhbh,ccm,ljkcbh,sblx,max(report_flag) as report_flag,max(report_time) as report_time from ks_ydsb_sbkszs GROUP BY ksjhbh,ccm,ljkcbh,sblx ) sbzs ON msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh WHERE kc.sczt = '0' AND kc.ksjhbh = ? AND kc.ljkcbh LIKE CONCAT('%', ?, '%') ORDER BY kc.ljkcbh, msg.KS_BPZWH LIMIT ? "}
{"@timestamp":"2025-07-30T05:24:02.726Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"==> Parameters: TEST2025001(String), 101(String), 10(Integer)"}
{"@timestamp":"2025-07-30T05:24:02.743Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjXq","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-30T05:24:02.744Z","severity":"INFO","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位详情查询完成，总记录数：3"}
{"@timestamp":"2025-07-30T05:24:02.744Z","severity":"INFO","service":"eeip-standalone-service","trace":"e85aa801dd86b31b","span":"e85aa801dd86b31b","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[空考位详情页面查询][/manager/identity/cxtj/kkwxq]reqModel:KkwXqReqModel[ksjhbh=TEST2025001,ccm=<null>,yclx=<null>,zkzh=<null>,kch=101,pageNum=1,pageSize=10,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwXqRespModel[pageInfo=PageInfo{pageNum=1, pageSize=10, size=3, startRow=1, endRow=3, total=3, pages=1, list=Page{count=true, pageNum=1, pageSize=10, startRow=0, endRow=10, total=3, pages=1, reasonable=true, pageSizeZero=false}[KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501003,xm=王五,zwh=03,yclx=6,zdsbsj=2025-07-29 09:00:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501002,xm=李四,zwh=03,yclx=4,zdsbsj=2025-07-29 09:06:00,sfsb=0,sbsjsj=<null>], KkwTjXqVO[kch=101,ljkcbh=LJ101,csmc=第一教学楼101教室,zkzh=202501001,xm=张三,zwh=04,yclx=7,zdsbsj=2025-07-29 09:01:00,sfsb=0,sbsjsj=<null>]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-30T05:24:17.386Z","severity":"INFO","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-30T05:24:17.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count "}
{"@timestamp":"2025-07-30T05:24:17.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T05:24:17.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:24:17.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? "}
{"@timestamp":"2025-07-30T05:24:17.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T05:24:17.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:24:17.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count "}
{"@timestamp":"2025-07-30T05:24:17.437Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T05:24:17.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:24:17.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? "}
{"@timestamp":"2025-07-30T05:24:17.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T05:24:17.462Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-30T05:24:17.463Z","severity":"INFO","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-30T05:24:17.463Z","severity":"INFO","service":"eeip-standalone-service","trace":"67a07ebb92d42315","span":"67a07ebb92d42315","parent":"","exportable":"false","pid":"30040","thread":"http-nio-8888-exec-9","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-30T05:24:59.831Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30040","thread":"Thread-118","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-30T05:25:11.621Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8184fc8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:12.182Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T05:25:14.399Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-30T05:25:14.400Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-30T05:25:14.402Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: aloneoha"}
{"@timestamp":"2025-07-30T05:25:20.187Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-30T05:25:20.194Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-30T05:25:20.504Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 283ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-30T05:25:20.719Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T05:25:20.978Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-30T05:25:21.626Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=91e86ee9-6ebb-3217-a543-0e15a26414d6"}
{"@timestamp":"2025-07-30T05:25:21.659Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-30T05:25:21.671Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-30T05:25:21.687Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-30T05:25:21.812Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$c262d927] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:21.814Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$7b951e57] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:21.928Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$5b2e91b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$656af991] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.449Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$410dd70e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$7d2f08b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.552Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.601Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$d05cb4c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:22.658Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8184fc8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-30T05:25:23.430Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-30T05:25:23.669Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 9235 ms"}
{"@timestamp":"2025-07-30T05:25:27.518Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-30T05:25:27.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:25:27.804Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:25:27.872Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:25:27.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-30T05:25:27.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-30T05:25:27.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-30T05:25:27.877Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-30T05:25:29.274Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-30T05:25:32.518Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-30T05:25:32.521Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:25:34.667Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-30T05:25:34.703Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-30T05:25:45.220Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-30T05:25:45.228Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-30T05:25:47.163Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-30T05:25:47.501Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-30T05:25:48.153Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-30T05:25:48.208Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-30T05:25:48.251Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:25:48.254Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"未配置默认平台类型，跳过上级平台DFS客户端初始化"}
{"@timestamp":"2025-07-30T05:25:50.929Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-30T05:25:50.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-30T05:25:50.931Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:25:50.943Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:25:50.944Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-30T05:25:53.253Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-30T05:25:53.363Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"当前数据库为[mysql]"}
{"@timestamp":"2025-07-30T05:25:54.293Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@423287bb],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@4a5dc3c9],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@7dd96f87],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@6d91fbe7],]"}
{"@timestamp":"2025-07-30T05:25:54.293Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-30T05:26:03.071Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-30T05:26:03.071Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-30T05:26:03.071Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-30T05:26:03.126Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-30T05:26:05.138Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-30T05:26:06.894Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-30T05:26:09.444Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-30T05:26:09.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:26:09.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-30T05:26:09.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-30T05:26:09.446Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T05:26:09.447Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-30T05:26:09.528Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-30T05:26:13.710Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-30T05:26:15.277Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-30T05:26:15.277Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-30T05:26:15.471Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-30T05:26:15.473Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-30T05:26:15.785Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 66.069 seconds (JVM running for 67.324)"}
{"@timestamp":"2025-07-30T05:26:15.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息"}
{"@timestamp":"2025-07-30T05:26:15.865Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-aloneoha.yml"}
{"@timestamp":"2025-07-30T05:26:15.870Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-aloneoha.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-30T05:26:16.239Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-30T05:26:16.360Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-30T05:26:16.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-30T05:26:16.381Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:16.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-30T05:26:16.382Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:16.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:16.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-30T05:26:16.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T05:26:16.419Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-30T05:26:16.473Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-30T05:26:17.448Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T05:26:17.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:26:17.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-30T05:26:17.477Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-30T05:26:18.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-30T05:26:18.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-30T05:26:18.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-30T05:26:18.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-30T05:26:18.261Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:26:18.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-30T05:26:18.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-30T05:26:18.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-30T05:26:18.263Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-30T05:26:18.263Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-30T05:26:18.273Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-30T05:26:18.275Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-30 13:26:18.269(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-30T05:26:18.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.298Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-30T05:26:18.298Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-30T05:26:18.298Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone_oha]"}
{"@timestamp":"2025-07-30T05:26:18.301Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.301Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-30T05:26:18.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.320Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-30T05:26:18.331Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.332Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-30T05:26:18.343Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.344Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.344Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-30T05:26:18.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.358Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.359Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-30T05:26:18.371Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.371Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.371Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-30T05:26:18.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.384Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-30T05:26:18.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.396Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-30T05:26:18.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-30T05:26:18.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.434Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.435Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T05:26:18.447Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-30T05:26:18.460Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.461Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.461Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-30T05:26:18.472Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.473Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.473Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T05:26:18.485Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.486Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.487Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-30T05:26:18.498Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.499Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.499Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.525Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T05:26:18.526Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.549Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.550Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-30T05:26:18.550Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.575Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-30T05:26:18.576Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.600Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.626Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.651Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.651Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.652Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.675Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.676Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.701Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.702Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_kkw_msg` ( `ID` varchar(32) NOT NULL, `KSJHBH` varchar(32) NOT NULL COMMENT '考试计划编号', `CCM` varchar(16) NOT NULL COMMENT '场次码', `BZHKDID` varchar(32) DEFAULT NULL COMMENT '标准化考点id', `BZHKCID` varchar(32) DEFAULT NULL COMMENT '标准化考场id', `SN` varchar(128) DEFAULT NULL COMMENT '设备序列号', `SCZT` varchar(6) DEFAULT NULL COMMENT '删除状态 0-正常 1-删除', `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间', `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间', `KS_ZKZH` varchar(128) DEFAULT NULL COMMENT '考生准考证号', `KS_BPZWH` varchar(32) DEFAULT NULL COMMENT '考生编排座位号', `KS_SJZWH` varchar(32) DEFAULT NULL COMMENT '考生实际座位号', `KS_KKW` varchar(32) DEFAULT NULL COMMENT '空考位', `ZCQSWZM` varchar(16) DEFAULT NULL COMMENT '座次起始位置码', `ZWBJFSM` varchar(16) DEFAULT NULL COMMENT '座位布局方式码', `ZWPLFSM` varchar(16) DEFAULT NULL COMMENT '座位排列方式码', `LJKCH` varchar(32) DEFAULT NULL COMMENT '逻辑考场号', `KCH` varchar(32) DEFAULT NULL COMMENT '考场号', `DEV_TYPE` varchar(16) DEFAULT NULL COMMENT '设备类型', `RCBZ` varchar(4) DEFAULT NULL COMMENT '入场备注0-默认 1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工 6-缺考（空位）7-无编排', `SJYXJ` int(10) DEFAULT NULL COMMENT '数据优先级 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场', `TIMESTAMP` datetime DEFAULT NULL COMMENT '时间戳', `YZFS` varchar(10) DEFAULT NULL COMMENT '验证方式', `YZJG` varchar(10) DEFAULT NULL COMMENT '验证结果', `SFRC` varchar(1) DEFAULT NULL COMMENT '是否入场', `RCSJ` varchar(17) DEFAULT NULL COMMENT '入场时间', `RCSJFZ` varchar(12) DEFAULT NULL COMMENT '入场时间分组', `RGYZJG` varchar(10) DEFAULT NULL COMMENT '人工验证结果 1-通过 0-不通过', `SJLY` varchar(10) DEFAULT NULL COMMENT '数据来源 web - 来自于 平台的页面 ；pad - 来自于 核验终端', `CZSJ` datetime DEFAULT NULL COMMENT '操作时间', PRIMARY KEY (`ID`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-30T05:26:18.703Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.727Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createKsKkwMsg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.741Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-30T05:26:18.741Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone_oha(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-30T05:26:18.753Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-30T05:26:18.754Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.755Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-30T05:26:18.765Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.767Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.768Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-30T05:26:18.779Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.780Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.780Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-30T05:26:18.792Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.793Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.794Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-30T05:26:18.807Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.808Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-30T05:26:18.808Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-30T05:26:18.833Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.834Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.834Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-30T05:26:18.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.855Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-30T05:26:18.855Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-30T05:26:18.926Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-30T05:26:18.926Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone_oha] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-30T05:26:18.927Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.927Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-30T05:26:18.937Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.938Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.938Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-30T05:26:18.950Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.950Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.951Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-30T05:26:18.961Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.962Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_flag(String)"}
{"@timestamp":"2025-07-30T05:26:18.973Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.974Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-30T05:26:18.974Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone_oha(String), ks_ydsb_sbkszs(String), report_time(String)"}
{"@timestamp":"2025-07-30T05:26:18.985Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:18.985Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-30T05:26:18.985Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-30T05:26:18.985Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-30T05:26:19.681Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"RMI TCP Connection(13)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Initializing Servlet 'dispatcherServlet'"}
{"@timestamp":"2025-07-30T05:26:19.954Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"RMI TCP Connection(13)-**************","class":"o.s.web.servlet.DispatcherServlet","rest":"Completed initialization in 272 ms"}
{"@timestamp":"2025-07-30T05:26:20.938Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"RMI TCP Connection(14)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-30T05:26:21.826Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"RMI TCP Connection(14)-**************","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: label not found"}
{"@timestamp":"2025-07-30T05:26:26.491Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"Thread-72","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_ATTENDANCE_JTXX,启动成功！"}
{"@timestamp":"2025-07-30T05:26:26.489Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"Thread-73","class":"com.xcwlkj.msgque.que.XcRocektMqConsumer","rest":"监听：JKYT_RSTJ_XXTS,启动成功！"}
{"@timestamp":"2025-07-30T05:26:37.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"收到请求开始：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>]"}
{"@timestamp":"2025-07-30T05:26:37.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT COUNT(DISTINCT kc.ljkcbh) AS total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) AS ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) AS wsb, CONCAT(ROUND(COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2), '%') AS reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ?) table_count "}
{"@timestamp":"2025-07-30T05:26:37.879Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T05:26:37.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjInfo_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:37.893Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==>  Preparing: SELECT COUNT(DISTINCT kc.ljkcbh) as total, COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb, COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb, CONCAT( ROUND( COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 / NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0), 2 ), '%' ) as reportRate FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND kc.ksjhbh = msg.KSJHBH AND msg.SCZT = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? LIMIT ? "}
{"@timestamp":"2025-07-30T05:26:37.893Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T05:26:37.909Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjInfo","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:37.927Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==>  Preparing: SELECT count(0) FROM (SELECT DISTINCT kc.kcbh AS kch, COALESCE(kc.bzhkcmc, js.jsmc) AS csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END AS sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') AS sbsj, kc.bzhkcid AS bzhkcid, kc.ljkcbh AS ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh) table_count "}
{"@timestamp":"2025-07-30T05:26:37.928Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"==> Parameters: TEST2025001(String)"}
{"@timestamp":"2025-07-30T05:26:37.939Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.K.selectKkwTjLb_COUNT","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-30T05:26:37.939Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==>  Preparing: SELECT DISTINCT kc.kcbh as kch, COALESCE(kc.bzhkcmc, js.jsmc) as csmc, CASE WHEN COUNT(msg.id) > 0 THEN '1' ELSE '0' END as sfsb, DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj, kc.bzhkcid as bzhkcid, kc.ljkcbh as ljkcbh FROM ks_kcxx kc LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH AND msg.SCZT = '0' AND kc.ksjhbh = msg.KSJHBH AND kc.ccm = msg.CCM LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0' WHERE kc.sczt = '0' AND kc.ksjhbh = ? GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh ORDER BY kc.kcbh LIMIT ? "}
{"@timestamp":"2025-07-30T05:26:37.941Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"==> Parameters: TEST2025001(String), 100(Integer)"}
{"@timestamp":"2025-07-30T05:26:37.953Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.m.KsKkwMsgMapper.selectKkwTjLb","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-30T05:26:37.974Z","severity":"INFO","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.service.impl.KsKkwMsgServiceImpl","rest":"空考位统计查询完成，总记录数：8，摘要信息：KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%]"}
{"@timestamp":"2025-07-30T05:26:37.975Z","severity":"INFO","service":"eeip-standalone-service","trace":"3875c5f538c9d20e","span":"3875c5f538c9d20e","parent":"","exportable":"false","pid":"22240","thread":"http-nio-8888-exec-2","class":"c.x.i.facade.manager.CxtjController","rest":"处理请求结束：[空考位统计查询][/manager/identity/cxtj/kkwtj]reqModel:KkwTjReqModel[ksjhbh=TEST2025001,ccm=<null>,sbzt=<null>,pageNum=1,pageSize=100,msgId=<null>,transChannel=<null>,channelSerialNo=<null>,channelDate=<null>,channelTime=<null>,token=<null>,loginToken=<null>,transCode=<null>,clientIp=<null>,merchantId=<null>,appPackage=<null>],respModel:KkwTjRespModel[pageInfo=PageInfo{pageNum=1, pageSize=100, size=8, startRow=1, endRow=8, total=8, pages=1, list=Page{count=true, pageNum=1, pageSize=100, startRow=0, endRow=100, total=8, pages=1, reasonable=true, pageSizeZero=false}[KkwTjItem[kch=101,csmc=第一教学楼101教室,sfsb=1,sbsj=2025-07-29 09:06:00,bzhkcid=TEST_KC001,ljkcbh=LJ101], KkwTjItem[kch=102,csmc=第一教学楼102教室,sfsb=1,sbsj=2025-07-29 09:03:00,bzhkcid=TEST_KC002,ljkcbh=LJ102], KkwTjItem[kch=103,csmc=第一教学楼103教室,sfsb=1,sbsj=2025-07-29 09:05:00,bzhkcid=TEST_KC003,ljkcbh=LJ103], KkwTjItem[kch=201,csmc=第一教学楼201教室,sfsb=1,sbsj=2025-07-29 09:08:00,bzhkcid=TEST_KC004,ljkcbh=LJ201], KkwTjItem[kch=202,csmc=第一教学楼202教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC005,ljkcbh=LJ202], KkwTjItem[kch=301,csmc=第二教学楼101教室,sfsb=1,sbsj=2025-07-29 11:01:00,bzhkcid=TEST_KC006,ljkcbh=LJ301], KkwTjItem[kch=302,csmc=第二教学楼102教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC007,ljkcbh=LJ302], KkwTjItem[kch=303,csmc=第二教学楼103教室,sfsb=0,sbsj=<null>,bzhkcid=TEST_KC008,ljkcbh=LJ303]], prePage=0, nextPage=0, isFirstPage=true, isLastPage=true, hasPreviousPage=false, hasNextPage=false, navigatePages=8, navigateFirstPage=1, navigateLastPage=1, navigatepageNums=[1]},summary=KkwTjInfoVO[total=8,ysb=5,wsb=3,reportRate=62.50%],msgId=<null>,transChannel=<null>,channelSerialNo=<null>,transCode=<null>,respDate=<null>,respTime=<null>,ext=<null>]"}
{"@timestamp":"2025-07-30T05:27:05.362Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==>  Preparing: SELECT id,version,created_by,creator_id,created_time,last_operator,last_operator_id,last_operator_time,ref_no,bucket_name,attachment_define_id,original_name,new_file_name,thumb_file_name,path,thumb_path,type,description,expire_time FROM attachment WHERE ( ( expire_time <= ? ) ) "}
{"@timestamp":"2025-07-30T05:27:05.363Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"==> Parameters: 2025-07-30 13:27:05(String)"}
{"@timestamp":"2025-07-30T05:27:05.374Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"idvScheduleTask-pool-0","class":"c.x.i.m.AttachmentMapper.selectByExample","rest":"<==      Total: 0"}
{"@timestamp":"2025-07-30T05:27:23.903Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22240","thread":"Thread-150","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
