/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.dos.SjsbTjxxDO;
import com.xcwlkj.identityverify.model.dto.cxtj.*;
import com.xcwlkj.identityverify.model.vo.cxtj.*;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.KshyqkVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 考生入场信息服务
 * <AUTHOR>
 * @version $Id: KsKsrcxxService.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Service
public interface KsKsrcxxService extends BaseService<KsKsrcxx> {


    void insertList(List<KsKsrcxx> ksrcxxList);

    void updateByPrimaryKeySelective(KsKsrcxx ksKsrcxx);

    KsrcqkxqcxVO ksrcqkxqcx(KsrcqkxqcxDTO dto);

    KsrctjVO ksrctj(KsrctjDTO dto);

    /**
     * 根据考试计划和考试场次查询考生入场统计信息
     * @param ksjhbh
     * @param ccm
     * @return
     */
    KshyqkVO kshyqkcx(String ksjhbh, String ccm);

    SjsbTjxxDO getSjsbTjxx(String ksjhbh, String ccm);

    KsrcsjsclbVO ksrcsjsclb(KsrcsjsclbDTO dto);

    void batchUpdateTbzt(List<KsKsrcxx> ksrcxxList);

    /**
     * 考生入场信息初始化
     * @param ksjhbh
     */
    void ksrcxxcsh(String ksjhbh);

    KsrcZeroLbVO ksrcZeroLb(KsrcZeroLbDTO dto);

    void ksrcZeroNotice(KsrcZeroNoticeDTO dto);

    RcwbsjLbVO rcwbsjLb(RcwbsjLbDTO dto);

    void ydzdsbzsNotice(YdzdsbzsNoticeDTO dto);

    /**
     * 考生入场统计逻辑考场
     * @param dto
     * @return
     */
    KsrctjljkcVO ksrctjljkc(KsrctjljkcDTO dto);

    KsrcqkxqcxxqVO ksrcqkxqcxxq(KsrcqkxqcxxqDTO dto);

    /**
     * 考生入场缺考情况导出
     * @param dto
     * @return
     */
    KsrcqkxqdcVO ksrcqkxqdc(KsrcqkxqdcDTO dto);

    /**
     * 上传考生核验信息到上级平台
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 上传结果统计信息（成功条数、失败条数、总条数）
     */
    UploadValidateInfoResultVO uploadValidateInfoToProvince(String ksjhbh, String ccm);

    /**
     * 上传考场入场人工（入场、缺考）信息到上级平台
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 上传结果统计信息（成功条数、失败条数、总条数）
     */
    UploadValidateInfoResultVO uploadManualEntranceToProvince(String ksjhbh, String ccm);

    /**
     * 上传考生照片到上级平台
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 上传结果统计信息（成功条数、失败条数、总条数）
     */
    UploadValidateInfoResultVO uploadCandidatePhotosToProvince(String ksjhbh, String ccm);
}