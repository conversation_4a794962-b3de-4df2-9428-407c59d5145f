package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 已上报入场信息DTO
 */
@Data
@NoArgsConstructor
public class YsbrcxxDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 上报时间 yyyy-MM-dd HH:mm:ss */
    private String sbsj;

    /** 考点代码 */
    private String kddm;

    /**
     * 入场状态
     * 0-人工审核不通过，1-人工审核通过，2-缺考
     */
    private String rczt;
}
