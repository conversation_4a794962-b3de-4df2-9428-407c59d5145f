package com.xcwlkj.identityverify.provincePlatform.request.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 考生入场总数DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KsrczsDTO implements Serializable {

    /** 入场总数列表 */
    private List<RczsItem> rczs;

    /** 考试信息 */
    private ExamItem exam;

    /**
     * 入场总数项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RczsItem implements Serializable {
        /** 上报时间yyyy-MM-dd HH:mm:ss */
        private String sbsj;
        /** 考生列表 */
        private List<KslbItem> kslb;
        /** 考点代码 */
        private String kddm;
        /** 考场号 */
        private String kch;
        /** 入场总数 */
        private String rczs;
        /** 逻辑考场编号 */
        private String ljkcbh;
    }

    /**
     * 考生列表项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KslbItem implements Serializable {
        /** 准考证号 */
        private String zkzh;
    }

    /**
     * 考试信息项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExamItem implements Serializable {
        /** 考试编号 */
        private String examPlanCode;
        /** 场次编号 */
        private String examSeqCode;
        /** 消息uuid */
        private String msgId;
    }
}
