package com.xcwlkj.identityverify.provincePlatform.request.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 空考位上报DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SbkkwDTO implements Serializable {

    /** 数据数组 */
    private List<SbkkwDataItem> dataArray;

    /** 操作时间yyyy-MM-dd HH:mm:ss */
    private String czsj;

    /** 时间戳 */
    private Long timestamp;

    /**
     * 空考位数据项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SbkkwDataItem implements Serializable {
        /** 考试编号 */
        private String examPlanCode;
        /** 机构编号 */
        private String orgCode;
        /** 设备序列号 */
        private String sn;
        /** 设备类型（172） */
        private String devType;
        /** 场次码 */
        private String ccm;
        /** 标准化考场id */
        private String bzhkcid;
        /** 座次起始位置码 */
        private String zcqswzm;
        /** 座位布局方式码 */
        private String zwbjfsm;
        /** 座位排列方式码 */
        private String zwplfsm;
        /** 考位列表 */
        private List<KwlbItem> kwlb;
    }

    /**
     * 考位列表项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KwlbItem implements Serializable {
        /** 逻辑考场号考点唯一 */
        private String ljkch;
        /** 考场号 */
        private String kch;
        /** 考生位号详情 */
        private List<KzwhxqItem> kzwhxq;
    }

    /**
     * 考生位号详情项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KzwhxqItem implements Serializable {
        /** 准考证号 */
        private String zkzh;
        /** 编排数据中考生座位号 */
        private String bpzwh;
        /** 实际座位号 */
        private String sjzwh;
        /** 入场备注1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位）7-无编排 */
        private String rcbz;
        /** 数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场 */
        private String sjyxj;
        /** 验收备核验信息 */
        private YsbhyxxItem ysbhyxx;
        /** 验收备入场信息 */
        private YsbrcxxItem ysbrcxx;
    }

    /**
     * 验收备核验信息项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YsbhyxxItem implements Serializable {
        /** 验证时间yyyy-MM-dd HH:mm:ss */
        private String yzsj;
        /** 核验结果 1-通过 0-不通过 */
        private String hyjg;
        /** 考点代码 */
        private String kddm;
        /** 刷证结果 1-通过 0-不通过 */
        private String szjg;
        /** 人脸识别结果 1-通过 0-不通过 */
        private String rlsbjg;
        /** 指纹认证结果 1-通过 0-不通过 */
        private String zwrzjg;
    }

    /**
     * 验收备入场信息项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YsbrcxxItem implements Serializable {
        /** 上报时间yyyy-MM-dd HH:mm:ss */
        private String sbsj;
        /** 考点代码 */
        private String kddm;
        /** 入场状态 0-人工审核不通过，1-人工审核通过，2-缺考 */
        private String rczt;
    }
}
