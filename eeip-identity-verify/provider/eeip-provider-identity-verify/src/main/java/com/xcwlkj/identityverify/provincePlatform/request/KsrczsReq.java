package com.xcwlkj.identityverify.provincePlatform.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 考生入场总数请求类
 */
@Data
public class KsrczsReq extends BaseRequest implements Serializable {

    /**
     * 加密的JSON字符串，使用固定秘钥hssfhy@2025~$#@!
     * 解密后的JSON结构包含：
     * - rczs: 入场总数数组
     *   - sbsj: 上报时间yyyy-MM-dd HH:mm:ss
     *   - kslb: 考生列表
     *     - zkzh: 准考证号
     *   - kddm: 考点代码
     *   - kch: 考场号
     *   - rczs: 入场总数
     *   - ljkcbh: 逻辑考场编号
     * - exam: 考试信息
     *   - examPlanCode: 考试编号
     *   - examSeqCode: 场次编号
     *   - msgId: 消息uuid
     */
    private String encrptJson;

    /**
     * 设备序列号
     */
    private String sbxlh;
}
