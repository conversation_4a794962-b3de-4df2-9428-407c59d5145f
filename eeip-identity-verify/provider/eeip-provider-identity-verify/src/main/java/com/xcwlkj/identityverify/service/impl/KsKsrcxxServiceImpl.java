/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.base.exception.BusinessException;
import com.xcwlkj.dfs.model.vo.TimePathVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.identityverify.handler.UpperHsDfsHandler;
import com.xcwlkj.identityverify.mapper.KsKsrcxxMapper;
import com.xcwlkj.identityverify.model.domain.KsBmxx;
import com.xcwlkj.identityverify.model.domain.KsKcxx;
import com.xcwlkj.identityverify.model.domain.KsKsrcxx;
import com.xcwlkj.identityverify.model.domain.KsKssjDistributeStatus;
import com.xcwlkj.identityverify.model.dos.KsLjkcRcRsDO;
import com.xcwlkj.identityverify.model.dos.KsRcRsDO;
import com.xcwlkj.identityverify.model.dos.SjsbTjxxDO;
import com.xcwlkj.identityverify.model.dto.attachment.GetAttachUrlDTO;
import com.xcwlkj.identityverify.model.dto.cxtj.*;
import com.xcwlkj.identityverify.model.enums.KsrcxxSbztEnum;
import com.xcwlkj.identityverify.model.enums.SbztwEnum;
import com.xcwlkj.identityverify.model.enums.SfhySftgEnum;
import com.xcwlkj.identityverify.model.vo.attachment.AttachFileVoItemVO;
import com.xcwlkj.identityverify.model.vo.attachment.GetAttachUrlVO;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.cxtj.*;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.SetValidateInfoReq;
import com.xcwlkj.identityverify.provincePlatform.request.SetValidateStuEnterReq;
import com.xcwlkj.identityverify.provincePlatform.request.SetValidatePicReq;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidateInfoDTO;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidateStuEnterDTO;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidatePicDTO;
import com.xcwlkj.identityverify.provincePlatform.request.items.SetValidateInfoItem;
import com.xcwlkj.identityverify.provincePlatform.request.items.SetValidateStuEnterItem;
import com.xcwlkj.identityverify.provincePlatform.request.items.SetValidatePicItem;
import com.xcwlkj.identityverify.provincePlatform.request.items.ValidateExamDO;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.identityverify.template.KsrcqkxqTemplate;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.KshyqkVO;
import com.xcwlkj.identityverify.util.CompressUtil;
import com.xcwlkj.identityverify.util.HsUtils;
import com.xcwlkj.identityverify.util.UnifyAccessUtils;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.common.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


/**
 * 考生入场信息服务
 *
 * <AUTHOR>
 * @version $Id: KsKsrcxxServiceImpl.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Service("ksKsrcxxService")
@Slf4j
public class KsKsrcxxServiceImpl extends BaseServiceImpl<KsKsrcxxMapper, KsKsrcxx> implements KsKsrcxxService {

    @Resource
    private KsKsrcxxMapper modelMapper;
    @Resource
    private KsKcxxService ksKcxxService;
    @Resource
    private KsBmxxService ksBmxxService;
    @Resource
    private CsXxjbxxService csXxjbxxService;
    @Resource
    private SbSbxxService sbSbxxService;
    @Resource
    private KsKssjDistributeStatusService ksKssjDistributeStatusService;
    @Resource
    private UnifyAccessUtils unifyAccessUtils;
    @Resource
    private AttachmentHandler attachmentHandler;
    @Resource
    private ProvincePlatformClient provincePlatformClient;
    @Resource
    private JySysDictService jySysDictService;
    @Resource
    private UpperHsDfsHandler upperHsDfsHandler;
    @Value("${xc.xcDfs.filePathPrefix}")
    private String filePathPrefix;
    @Value("${xc.temp.generatePath}")
    private String generatePath;
    // 创建HTTP客户端（在类级别初始化一次）
    private static final CloseableHttpClient httpClient = HttpClients.custom()
            .setMaxConnTotal(50)
            .setMaxConnPerRoute(20)
            .setConnectionTimeToLive(10, TimeUnit.SECONDS)
            .build();



    @Override
    public void insertList(List<KsKsrcxx> ksrcxxList) {
        modelMapper.insertList(ksrcxxList);
    }
    @Override
    public void updateByPrimaryKeySelective(KsKsrcxx ksKsrcxx){
        modelMapper.updateByPrimaryKeySelective(ksKsrcxx);
    }
    @Override
    public KsrcqkxqcxVO ksrcqkxqcx(KsrcqkxqcxDTO ksrcqkxqcxDTO) {
        String ksjhbh = ksrcqkxqcxDTO.getKsjhbh();
        String csbh = ksrcqkxqcxDTO.getCsbh();
        String cslx = ksrcqkxqcxDTO.getCslx();
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(csbh, cslx);
//        List<KsKcxx> bzhkcListByKsjhbh = ksKcxxMapper.getBzhkcList(ksjhbh);
//        List<String> bzhkcidsByKsjhbh = bzhkcListByKsjhbh.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
//        bzhkcids.retainAll(bzhkcidsByKsjhbh);
        KsrcqkxqcxVO ksrcqkxqcxVO = new KsrcqkxqcxVO();
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderCol())) {
            ksrcqkxqcxDTO.setOrderCol("KCBH,ZWH_PX");
        }
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderType())) {
            ksrcqkxqcxDTO.setOrderType("ASC");
        }
        Page<Object> page = PageHelper.startPage(ksrcqkxqcxDTO.getPageNum(), ksrcqkxqcxDTO.getPageSize());
        List<KsrcqkDatasItemVO> ksrcqkxqcxVOList = modelMapper.ksrcqkxqcx(ksrcqkxqcxDTO, bzhkcids);
        ksrcqkxqcxVOList.forEach(k -> {
            String sfzh = k.getSfzh();
            // 身份证号脱敏
            k.setSfzh(HsUtils.EncryptionSFZH(sfzh));
        });
        ksrcqkxqcxVO.setDatas(ksrcqkxqcxVOList);
        ksrcqkxqcxVO.setTotalRows((int) page.getTotal());
        return ksrcqkxqcxVO;
    }

    @Override
    public KsrctjVO ksrctj(KsrctjDTO ksrctjDTO) {
        List<String> kdbhList = ksrctjDTO.getCsbh() == null ? null : Collections.singletonList(ksrctjDTO.getCsbh());
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(ksrctjDTO.getCsbh(), ksrctjDTO.getCslx());
        List<KsRcRsDO> ksrckcs = modelMapper.cxkcrcrs(ksrctjDTO.getKsjhbh(), ksrctjDTO.getCcm(), kdbhList, bzhkcids);
        List<KsrctjDatasItemVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ksrckcs)) {
            Map<String, List<KsRcRsDO>> map = new HashMap<>();
            for (KsRcRsDO ksrckc : ksrckcs) {
                List<KsRcRsDO> value = new ArrayList<>();
                if (map.containsKey(ksrckc.getWlkcbh())) {
                    value.addAll(map.get(ksrckc.getWlkcbh()));
                }
                value.add(ksrckc);
                map.put(ksrckc.getWlkcbh(), value);
            }
            for (String key : map.keySet()) {
                KsrctjDatasItemVO ksrctjDatasItemVO = new KsrctjDatasItemVO();
                List<KsRcRsDO> ksRcRsDOS = map.get(key);
                // 总人数
                int zrs = 0;
                int yrcrs = 0;
                int wrcrs = 0;
                int dqdrs = 0;
                for (KsRcRsDO ksRcRsDO : ksRcRsDOS) {
                    zrs += ksRcRsDO.getRs();
                    if (StringUtil.equals(ksRcRsDO.getSfrc(), "1")) {
                        yrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "0")) {
                        wrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "9")) {
                        dqdrs = ksRcRsDO.getRs();
                    }
                }
                // TODO 也许需要考虑考场名称为空时的情况
                ksrctjDatasItemVO.setCsbh(ksRcRsDOS.get(0).getWlkcbh());
                ksrctjDatasItemVO.setCsmc(ksRcRsDOS.get(0).getKcmc());
                ksrctjDatasItemVO.setWlcsmc(ksRcRsDOS.get(0).getWlkcmc());
                ksrctjDatasItemVO.setZrs(zrs);
                ksrctjDatasItemVO.setYrcrs(yrcrs);
                ksrctjDatasItemVO.setWrcrs(wrcrs);
                ksrctjDatasItemVO.setDqdrs(dqdrs);
                ksrctjDatasItemVO.setKcbh(ksRcRsDOS.get(0).getKcbh());
                list.add(ksrctjDatasItemVO);
            }
        }
        KsrctjVO ksrctjVO = new KsrctjVO();
        ksrctjVO.setDatas(list);
        return ksrctjVO;
    }

    @Override
    public KshyqkVO kshyqkcx(String ksjhbh, String ccm) {
        return modelMapper.kshyqkcx(ksjhbh, ccm);
    }

    @Override
    public SjsbTjxxDO getSjsbTjxx(String ksjhbh, String ccm) {
        return modelMapper.getSjsbTjxx(ksjhbh, ccm);
    }

    @Override
    public int updateByExampleSelective(KsKsrcxx ksKsrcxx, Example example) {
        ksKsrcxx.setUpdateTime(DateUtil.getCurrentDT());

        ksKsrcxx.setSbzt("0");  // 上报状态
        ksKsrcxx.setTbzt("0");  // 同步状态

        return modelMapper.updateByExampleSelective(ksKsrcxx, example);
    }

    @Override
    public KsrcsjsclbVO ksrcsjsclb(KsrcsjsclbDTO dto) {
        Example example = new Example(KsKsrcxx.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", dto.getKsjh());
        criteria.andEqualTo("ccm", dto.getKscc());
        if (StringUtil.isNotBlank(dto.getXm())) {
            criteria.andLike("ksxm", dto.getXm());
        }
        if (StringUtil.isNotBlank(dto.getZjhm())) {
            criteria.andLike("sfzh", dto.getZjhm());
        }
        if (StringUtil.isNotBlank(dto.getSbzt())) {
            Example.Criteria and = example.and();
            and.andLike("sbzt", dto.getSbzt());
            if (StringUtil.equals(dto.getSbzt(), "0")) {
                and.orIsNull("sbzt");
            }
        }

        List<KsKsrcxx> ksKsrcxxes = modelMapper.selectByExample(example);

        List<KsrcsjscItemVO> ksrcsjscList = new ArrayList<>();
        for (KsKsrcxx ksKsrcxx : ksKsrcxxes) {
            KsrcsjscItemVO ksrcsjscItemVO = new KsrcsjscItemVO();
            ksrcsjscItemVO.setXm(ksKsrcxx.getKsxm());
            ksrcsjscItemVO.setZkzh(ksKsrcxx.getZkzh());
            ksrcsjscItemVO.setZjhm(HsUtils.EncryptionSFZH(ksKsrcxx.getSfzh()));

            if (StringUtil.isNotBlank(ksKsrcxx.getRgyzjg())) {
                if (StringUtil.equals(ksKsrcxx.getRgyzjg(), "1")) {
                    ksrcsjscItemVO.setYzfs("人工验证");
                    ksrcsjscItemVO.setHyjg(SfhySftgEnum.TG.getDesc());
                } else if (StringUtil.equals(ksKsrcxx.getRgyzjg(), "0")) {
                    ksrcsjscItemVO.setYzfs("人工验证");
                    ksrcsjscItemVO.setHyjg(SfhySftgEnum.WTG.getDesc());
                }
            } else if (StringUtil.isNotBlank(ksKsrcxx.getYzfs())) {
                String yzfs = ksKsrcxx.getYzfs();
                if (StringUtil.isNotBlank(yzfs)) {
                    yzfs = yzfs.replace(SfhySftgEnum.SFZYZ.getCode(), SfhySftgEnum.SFZYZ.getDesc() + "-")
                            .replace(SfhySftgEnum.RLYZ.getCode(), SfhySftgEnum.RLYZ.getDesc() + "-")
                            .replace(SfhySftgEnum.ZWYZ.getCode(), SfhySftgEnum.ZWYZ.getDesc() + "-");
                    ksrcsjscItemVO.setYzfs(yzfs.substring(0, yzfs.length() - 1));
                } else {
                    ksrcsjscItemVO.setYzfs("-");
                }
                String yzjg = ksKsrcxx.getYzjg();
                if (StringUtil.isNotBlank(yzjg)) {
                    yzjg = yzjg.replace(SfhySftgEnum.TG.getCode(), SfhySftgEnum.TG.getDesc() + "-")
                            .replace(SfhySftgEnum.CY.getCode(), SfhySftgEnum.CY.getDesc() + "-")
                            .replace(SfhySftgEnum.SJKWTG.getCode(), SfhySftgEnum.SJKWTG.getDesc() + "-");
                    ksrcsjscItemVO.setHyjg(yzjg.substring(0, yzjg.length() - 1));
                } else {
                    ksrcsjscItemVO.setHyjg("-");
                }
            }
            if (StringUtil.equals(ksKsrcxx.getSfrc(), SfhySftgEnum.WRC.getCode())) {
                ksrcsjscItemVO.setYzfs("-");
                ksrcsjscItemVO.setHyjg(SfhySftgEnum.WRC.getDesc());
            }

            String sbzt = ksKsrcxx.getSbzt();
            if (StringUtil.isNotBlank(sbzt)) {
                ksrcsjscItemVO.setScjg(KsrcxxSbztEnum.get(sbzt).getDesc());
            } else {
                ksrcsjscItemVO.setScjg("-");
            }
            String tbzt = ksKsrcxx.getTbzt();
            if(StringUtils.isNotBlank(tbzt)){
                ksrcsjscItemVO.setTbjg(KsrcxxSbztEnum.get(tbzt).getDesc());
            }else{
                ksrcsjscItemVO.setTbjg("-");
            }
            ksrcsjscList.add(ksrcsjscItemVO);
        }

        KsrcsjsclbVO ksrcsjsclbVO = new KsrcsjsclbVO();
        ksrcsjsclbVO.setKsrcsjscList(ksrcsjscList);
        return ksrcsjsclbVO;
    }

    @Override
    public void batchUpdateTbzt(List<KsKsrcxx> ksrcxxList) {
        modelMapper.batchUpdateTbzt(ksrcxxList);
    }

    @Override
    public void ksrcxxcsh(String ksjhbh) {
        modelMapper.initByKsjhbh(ksjhbh);
    }

    @Override
    public KsrcZeroLbVO ksrcZeroLb(KsrcZeroLbDTO dto) {
        KsrcZeroLbVO result = new KsrcZeroLbVO();
        Page<Object> page = new Page<>();
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<ZeroKcItemVO> zeroKcItemVOS = modelMapper.ksrcZeroLb(dto);
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            result.setTotalRows((int) page.getTotal());
        }
        result.setZeroKcList(zeroKcItemVOS);

        if (zeroKcItemVOS.isEmpty()) {
            return result;
        }
        List<String> bzhkcids = zeroKcItemVOS.stream().map(ZeroKcItemVO::getCsdm).collect(Collectors.toList());

        Map<String, String> kcmcMap = ksKcxxService.getKcmcMap(dto.getKsjhbh(), bzhkcids);

        for (ZeroKcItemVO zeroKcItemVO : zeroKcItemVOS) {
            zeroKcItemVO.setKcmc(kcmcMap.get(zeroKcItemVO.getCsdm()));
        }
        return result;
    }

    @Override
    public void ksrcZeroNotice(KsrcZeroNoticeDTO dto) {
        Example emKc = new Example(KsKcxx.class);
        emKc.createCriteria()
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andIn("bzhkcid", dto.getKcidList());
        emKc.setDistinct(true);
        List<KsKcxx> kdKcxxes = ksKcxxService.selectListByExample(emKc);
        sendDataMissingToKc(dto.getKsjhbh(), dto.getCcm(), kdKcxxes);
    }

    @Override
    public RcwbsjLbVO rcwbsjLb(RcwbsjLbDTO dto) {
        RcwbsjLbVO result = new RcwbsjLbVO();

        Page<Object> page = new Page<>();
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
        List<RcwbsjItemVO> rcwbsjItemVOS = modelMapper.rcwbsjLb(dto);
        result.setRcwbsjList(rcwbsjItemVOS);

        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            result.setTotalRows((int) page.getTotal());
        }
        return result;
    }

    @Override
    public void ydzdsbzsNotice(YdzdsbzsNoticeDTO dto) {
        Map<String, String> sbzxztMap = sbSbxxService.zxztByXlhs(dto.getSbList());
        if (sbzxztMap == null || sbzxztMap.isEmpty()) {
            throw new BusinessException("设备状态查询失败");
        }

        List<String> zxsbxlhList = sbzxztMap.entrySet().stream().filter(entry -> StringUtils.equals(entry.getValue(), "1")).map(Map.Entry::getKey).collect(Collectors.toList());
        if (zxsbxlhList.isEmpty()) {
            throw new BusinessException("无在线设备");
        }


        Example emRcxx = new Example(KsKsrcxx.class);
        emRcxx.createCriteria()
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andIn("sbxlh", zxsbxlhList);
        emRcxx.setDistinct(true);
        emRcxx.selectProperties("ksjhbh", "ccm", "kdbh", "bzhkdid", "kcbh", "ljkcbh", "sbxlh");
        List<KsKsrcxx> ksKsrcxxes = modelMapper.selectByExample(emRcxx);
        if (ksKsrcxxes.size() > 0) {
            Map<String, List<KsKsrcxx>> ksKsrcxxMap = ksKsrcxxes.stream().collect(Collectors.groupingBy(KsKsrcxx::getBzhkdid));
            ksKsrcxxMap.forEach((kdbh, rcxxList) -> {
                JSONObject dataJson = new JSONObject();
                dataJson.put("ksbh", dto.getKsjhbh());
                dataJson.put("ccm", dto.getCcm());
                dataJson.put("kddm", kdbh);
                for (KsKsrcxx ksKsrcxx : rcxxList) {
                    JSONArray kclb = new JSONArray();
                    JSONObject kcxxJson = new JSONObject();
                    kcxxJson.put("kch", ksKsrcxx.getKcbh());
                    kcxxJson.put("ljkcbh", ksKsrcxx.getLjkcbh());
                    kclb.add(kcxxJson);
                    dataJson.put("kclb", kclb);
                    unifyAccessUtils.sendMessage(ksKsrcxx.getSbxlh(), dataJson, "ACTIVE_BZKS_RCZS");
                }
            });
        }
    }

    @Override
    public KsrctjljkcVO ksrctjljkc(KsrctjljkcDTO dto) {
        List<String> kdbhList = dto.getCsbh() == null ? null : Collections.singletonList(dto.getCsbh());
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(dto.getCsbh(), dto.getCslx());
        List<KsLjkcRcRsDO> ksrckcs = modelMapper.cxljkcrcrs(dto.getKsjhbh(), dto.getCcm(), kdbhList, bzhkcids);
        List<KsrctjljkcDatasItemVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ksrckcs)) {
            Map<String, List<KsLjkcRcRsDO>> map = new HashMap<>();
            for (KsLjkcRcRsDO ksrckc : ksrckcs) {
                List<KsLjkcRcRsDO> value = new ArrayList<>();
                if (map.containsKey(ksrckc.getLjkcbh())) {
                    value.addAll(map.get(ksrckc.getLjkcbh()));
                }
                value.add(ksrckc);
                map.put(ksrckc.getLjkcbh(), value);
            }
            for (String key : map.keySet()) {
                KsrctjljkcDatasItemVO ksrctjDatasItemVO = new KsrctjljkcDatasItemVO();
                List<KsLjkcRcRsDO> ksRcRsDOS = map.get(key);
                // 总人数
                int zrs = 0;
                int yrcrs = 0;
                int wrcrs = 0;
                int dqdrs = 0;
                for (KsLjkcRcRsDO ksRcRsDO : ksRcRsDOS) {
                    zrs += ksRcRsDO.getRs();
                    if (StringUtil.equals(ksRcRsDO.getSfrc(), "1")) {
                        yrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "0")) {
                        wrcrs = ksRcRsDO.getRs();
                    } else if (StringUtil.equals(ksRcRsDO.getSfrc(), "9")) {
                        dqdrs = ksRcRsDO.getRs();
                    }
                }
                // TODO 也许需要考虑考场名称为空时的情况
                ksrctjDatasItemVO.setLjkcbh(ksRcRsDOS.get(0).getLjkcbh());
                ksrctjDatasItemVO.setCsbh(ksRcRsDOS.get(0).getWlkcbh());
                ksrctjDatasItemVO.setCsmc(ksRcRsDOS.get(0).getKcmc());
                ksrctjDatasItemVO.setWlcsmc(ksRcRsDOS.get(0).getWlkcmc());
                ksrctjDatasItemVO.setZrs(zrs);
                ksrctjDatasItemVO.setYrcrs(yrcrs);
                ksrctjDatasItemVO.setWrcrs(wrcrs);
                ksrctjDatasItemVO.setDqdrs(dqdrs);
                ksrctjDatasItemVO.setKcbh(ksRcRsDOS.get(0).getKcbh());
                list.add(ksrctjDatasItemVO);
            }
        }
        KsrctjljkcVO ksrctjVO = new KsrctjljkcVO();
        ksrctjVO.setDatas(list);
        return ksrctjVO;
    }

    @Override
    public KsrcqkxqcxxqVO ksrcqkxqcxxq(KsrcqkxqcxxqDTO dto) {
        KsrcqkxqcxxqVO result = new KsrcqkxqcxxqVO();
        Example ksrcEg = new Example(KsKsrcxx.class);
        ksrcEg.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("ksh", dto.getKsh());
        List<KsKsrcxx> ksrcxxes = this.selectListByExample(ksrcEg);
        Example ksbmEg = new Example(KsBmxx.class);
        ksbmEg.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhid", dto.getKsjhbh())
                .andEqualTo("ksh", dto.getKsh());
//        ksbmEg.selectProperties("zp");
        List<KsBmxx> ksBmxxes = ksBmxxService.selectListByExample(ksbmEg);

        if (!ksrcxxes.isEmpty()) {
            KsKsrcxx ksrcxx = ksrcxxes.get(0);
            int type;
            if (StringUtils.isNotBlank(ksrcxx.getRcrlzp())) {
                if (ksrcxx.getRcrlzp().contains("/")) {
                    type = 1;
                } else {
                    type = 0;
                }
                TimePathVO timePathVO = XcDfsClient.timePath(2L, type, ksrcxx.getRcrlzp());
                if (!timePathVO.getFileList().isEmpty()) {
                    result.setXczp(filePathPrefix + timePathVO.getFileList().get(0).getUrl());
                }
            }
            if (StringUtils.isNotBlank(ksrcxx.getSfzp())) {
                if (ksrcxx.getSfzp().contains("/")) {
                    type = 1;
                } else {
                    type = 0;
                }
                TimePathVO _timePathVO = XcDfsClient.timePath(2L, type, ksrcxx.getSfzp());
                if (!_timePathVO.getFileList().isEmpty()) {
                    result.setSfzzp(filePathPrefix + _timePathVO.getFileList().get(0).getUrl());
                }
            }
            if (!ksBmxxes.isEmpty()) {
                KsBmxx ksBmxx = ksBmxxes.get(0);
                if (StringUtils.isNotBlank(ksBmxx.getZp())) {
                    if (ksBmxx.getZp().contains("/")) {
                        type = 1;
                    } else {
                        type = 0;
                    }
                    TimePathVO timePathVO = XcDfsClient.timePath(2L, type, ksBmxx.getZp());
                    if (!timePathVO.getFileList().isEmpty()) {
                        result.setCjzp(filePathPrefix + timePathVO.getFileList().get(0).getUrl());

                    }
                }
            }
        }
        return result;
    }

    @Override
    public KsrcqkxqdcVO ksrcqkxqdc(KsrcqkxqdcDTO dto) {
        StopWatch sw = new StopWatch("ksrcqkxqdc");
        sw.start("查询,加工数据");
        KsrcqkxqcxDTO ksrcqkxqcxDTO = new KsrcqkxqcxDTO();
        BeanUtils.copyProperties(dto, ksrcqkxqcxDTO);
        String ksjhbh = ksrcqkxqcxDTO.getKsjhbh();
        String csbh = ksrcqkxqcxDTO.getCsbh();
        String cslx = ksrcqkxqcxDTO.getCslx();
        List<String> bzhkcids = csXxjbxxService.getBzhkcidsByCsbhAndCslx(csbh, cslx);
//        List<KsKcxx> bzhkcListByKsjhbh = ksKcxxMapper.getBzhkcList(ksjhbh);
//        List<String> bzhkcidsByKsjhbh = bzhkcListByKsjhbh.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
//        bzhkcids.retainAll(bzhkcidsByKsjhbh);
        KsrcqkxqcxVO ksrcqkxqcxVO = new KsrcqkxqcxVO();
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderCol())) {
            ksrcqkxqcxDTO.setOrderCol("KCBH,ZWH_PX,CCM");
        }
        if (StringUtil.isBlank(ksrcqkxqcxDTO.getOrderType())) {
            ksrcqkxqcxDTO.setOrderType("ASC");
        }
        List<KsrcqkDatasItemVO> ksrcqkxqcxVOList = modelMapper.ksrcqkxqcx(ksrcqkxqcxDTO, bzhkcids);
        ksrcqkxqcxVOList.forEach(k -> {
            String sfzh = k.getSfzh();
            // 身份证号脱敏
            k.setSfzh(HsUtils.EncryptionSFZH(sfzh));
        });
        sw.stop();
// 创建临时目录
        String dateStr = DateUtil.getCurrentDate("yyyy-MM-dd_HHmmss");
        String folderPath = generatePath + File.separator + "ksrcqkqk" + File.separator + ksjhbh + File.separator + dateStr;
        Path path = Paths.get(folderPath);
        try {
            Files.createDirectories(path);
        } catch (IOException e) {
            throw new RuntimeException("创建临时目录失败：" + folderPath, e);
        }

        String zipFilePath = folderPath + ".zip";
        List<String> fileList = new ArrayList<>();
        try {
            // 导出Excel文件
            sw.start("导出Excel文件");
            String excelPath = exportKsrcqkxqExcel(folderPath, ksrcqkxqcxVOList);
//            fileList.add(excelPath);
            sw.stop();

            // 获取不为空的照片文件
            sw.start("获取照片文件");
            Map<String, String> fileIdToNameMap = new HashMap<>();
            Map<String, String> fileIdToPathMap = new HashMap<>();
            List<String> photoFiles = exportKsrcqkxqPhoto(folderPath, dto.getKsjhbh(), dto.getCcm(), fileIdToNameMap, fileIdToPathMap, ksrcqkxqcxVOList);
            fileList.addAll(photoFiles);

            // 下载照片文件并重命名
            for (String fileId : photoFiles) {
                String fileName = fileIdToNameMap.get(fileId);
                String filePath = fileIdToPathMap.get(fileId);
                if (StringUtil.isNotBlank(filePath)) {
                    String newFilePath = folderPath + File.separator + fileName;
                    URL url = new URL(filePath);
                    File file = new File(newFilePath);
                    FileUtils.copyURLToFile(url, file);
                    log.info("下载文件成功，fileId:{} filePath:{} newFilePath:{}", fileId, filePath, newFilePath);
                }
            }

            sw.stop();
            sw.start("压缩文件");
            // 压缩文件
            String zip = CompressUtil.zip(folderPath, "");
            if (StringUtil.isBlank(zip)) {
                throw new BusinessException("打包失败");
            }
            log.info("压缩文件成功，zipFilePath:{}", zipFilePath);
            sw.stop();
            sw.start("上传压缩包");
            // 上传压缩包
            DateTime expireTime = DateUtil.offsetHour(new Date(), 2);
            UploadAttachmentReturnUrlVO uploadAttachmentReturnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(zipFilePath, expireTime, null);
            sw.stop();
            // 返回上传后的附件路径
            KsrcqkxqdcVO result = new KsrcqkxqdcVO();
            result.setDclj(uploadAttachmentReturnUrlVO.getAttachmentUrl());
            log.info(sw.prettyPrint());
            return result;

        } catch (IOException e) {
            log.error("文件处理或压缩失败，zipFilePath:{} fileList:{}，异常信息：{}", zipFilePath, fileList, e);
            throw new BusinessException("打包失败");
        } finally {
            // 删除临时文件
            for (String filePath : fileList) {
                try {
                    Files.deleteIfExists(Paths.get(filePath));
                } catch (IOException e) {
                    log.warn("删除临时文件失败：{}", filePath, e);
                }
            }
            try {
                Files.deleteIfExists(Paths.get(zipFilePath));
            } catch (IOException e) {
                log.warn("删除ZIP文件失败：{}", zipFilePath, e);
            }
            // 删除临时目录（递归删除）
            try {
                deleteDirectoryRecursively(Paths.get(folderPath));
            } catch (IOException e) {
                log.warn("递归删除临时目录失败：{}", folderPath, e);
            }
        }
    }

    @Override
    public UploadValidateInfoResultVO uploadValidateInfoToProvince(String ksjhbh, String ccm) {
        log.info("开始上传考生核验信息到上级平台，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        UploadValidateInfoResultVO result = new UploadValidateInfoResultVO();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailCount(0);
        result.setBatchCount(0);
        result.setAllSuccess(true);

        try {
            // 1. 统计总数
            int totalCount = modelMapper.countForUploadToProvince(ksjhbh, ccm);
            result.setTotalCount(totalCount);

            if (totalCount == 0) {
                result.setMessage("没有需要上传的考生入场信息");
                log.info("没有需要上传的考生入场信息，考试计划编号：{}，场次码：{}", ksjhbh, ccm);
                return result;
            }

            log.info("共找到{}条考生入场信息需要上传", totalCount);

            // 2. 分批处理，每批100条
            int batchSize = 100;
            int batchCount = (totalCount + batchSize - 1) / batchSize;
            result.setBatchCount(batchCount);

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (int i = 0; i < batchCount; i++) {
                int offset = i * batchSize;
                log.info("开始处理第{}批数据，偏移量：{}，批次大小：{}", i + 1, offset, batchSize);

                try {
                    // 查询当前批次的数据
                    List<KsKsrcxx> batchData = modelMapper.selectForUploadToProvince(ksjhbh, ccm, offset, batchSize);

                    if (CollectionUtils.isEmpty(batchData)) {
                        log.warn("第{}批数据为空，跳过", i + 1);
                        continue;
                    }

                    // 按设备序列号分组
                    Map<String, List<KsKsrcxx>> deviceGroupMap = batchData.stream()
                            .collect(Collectors.groupingBy(ksrcxx ->
                                StringUtil.isNotBlank(ksrcxx.getSbxlh()) ? ksrcxx.getSbxlh() : "DEFAULT_DEVICE_SN"));

                    // 按设备序列号分组上传
                    boolean allBatchSuccess = true;
                    for (Map.Entry<String, List<KsKsrcxx>> entry : deviceGroupMap.entrySet()) {
                        String deviceSn = entry.getKey();
                        List<KsKsrcxx> deviceData = entry.getValue();

                        log.info("第{}批数据中设备{}的数据量：{}", i + 1, deviceSn, deviceData.size());

                        boolean deviceBatchSuccess = uploadBatchToProvince(deviceData, ksjhbh, ccm, deviceSn);

                        if (!deviceBatchSuccess) {
                            allBatchSuccess = false;
                            log.error("第{}批数据中设备{}上传失败，数量：{}", i + 1, deviceSn, deviceData.size());
                        } else {
                            log.info("第{}批数据中设备{}上传成功，数量：{}", i + 1, deviceSn, deviceData.size());
                        }
                    }

                    if (allBatchSuccess) {
                        successCount += batchData.size();
                        log.info("第{}批数据上传成功，数量：{}", i + 1, batchData.size());
                    } else {
                        failCount += batchData.size();
                        result.setAllSuccess(false);
                        errorMessages.append("第").append(i + 1).append("批数据上传失败；");
                        log.error("第{}批数据上传失败，数量：{}", i + 1, batchData.size());
                    }

                } catch (Exception e) {
                    failCount += Math.min(batchSize, totalCount - offset);
                    result.setAllSuccess(false);
                    errorMessages.append("第").append(i + 1).append("批数据处理异常：").append(e.getMessage()).append("；");
                    log.error("第{}批数据处理异常", i + 1, e);
                }
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);

            if (result.getAllSuccess()) {
                result.setMessage("所有数据上传成功");
            } else {
                result.setMessage("部分数据上传失败：" + errorMessages.toString());
            }

            log.info("考生核验信息上传完成，总数：{}，成功：{}，失败：{}，批次数：{}",
                    totalCount, successCount, failCount, batchCount);

        } catch (Exception e) {
            result.setAllSuccess(false);
            result.setMessage("上传过程中发生异常：" + e.getMessage());
            log.error("上传考生核验信息到上级平台异常", e);
        }

        return result;
    }

    @Override
    public UploadValidateInfoResultVO uploadManualEntranceToProvince(String ksjhbh, String ccm) {
        log.info("开始上传考场入场人工（入场、缺考）信息到上级平台，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        UploadValidateInfoResultVO result = new UploadValidateInfoResultVO();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailCount(0);
        result.setBatchCount(0);
        result.setAllSuccess(true);

        try {
            // 1. 统计总数
            int totalCount = modelMapper.countForManualEntranceUpload(ksjhbh, ccm);
            result.setTotalCount(totalCount);

            if (totalCount == 0) {
                result.setMessage("没有需要上传的考生人工入场信息");
                log.info("没有需要上传的考生人工入场信息，考试计划编号：{}，场次码：{}", ksjhbh, ccm);
                return result;
            }

            // 2. 分批处理数据
            int batchSize = 100;
            int batchCount = (totalCount + batchSize - 1) / batchSize;
            result.setBatchCount(batchCount);

            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < batchCount; i++) {
                int offset = i * batchSize;
                List<KsKsrcxx> batchData = modelMapper.selectForManualEntranceUpload(ksjhbh, ccm, offset, batchSize);

                if (batchData.isEmpty()) {
                    continue;
                }

                // 按设备序列号分组
                Map<String, List<KsKsrcxx>> deviceGroups = batchData.stream()
                        .collect(Collectors.groupingBy(item ->
                            StringUtils.isNotBlank(item.getSbxlh()) ? item.getSbxlh() : "unknown"));

                // 为每个设备序列号分别上传
                for (Map.Entry<String, List<KsKsrcxx>> entry : deviceGroups.entrySet()) {
                    String deviceSn = entry.getKey();
                    List<KsKsrcxx> deviceData = entry.getValue();

                    boolean uploadSuccess = uploadManualEntranceBatchToProvince(deviceData, ksjhbh, ccm, deviceSn);
                    if (uploadSuccess) {
                        successCount += deviceData.size();
                    } else {
                        failCount += deviceData.size();
                        result.setAllSuccess(false);
                    }
                }

                log.info("批次 {}/{} 处理完成，当前成功：{}，失败：{}", i + 1, batchCount, successCount, failCount);
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);

            if (result.getAllSuccess()) {
                result.setMessage("考生人工入场信息上传成功");
            } else {
                result.setMessage(String.format("考生人工入场信息上传完成，成功：%d，失败：%d", successCount, failCount));
            }

            log.info("考生人工入场信息上传完成，总数：{}，成功：{}，失败：{}，批次数：{}",
                    totalCount, successCount, failCount, batchCount);

        } catch (Exception e) {
            result.setAllSuccess(false);
            result.setMessage("上传过程中发生异常：" + e.getMessage());
            log.error("上传考生人工入场信息到上级平台异常", e);
        }

        return result;
    }

    /**
     * 上传一批考生人工入场信息到上级平台
     * @param batchData 批次数据
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param deviceSn 设备序列号
     * @return 是否成功
     */
    private boolean uploadBatchToProvince(List<KsKsrcxx> batchData, String ksjhbh, String ccm, String deviceSn) {
        try {
            // 1. 转换数据格式
            List<SetValidateInfoItem> hyxxList = new ArrayList<>();
            List<String> ksrcbhList = new ArrayList<>();

            for (KsKsrcxx ksrcxx : batchData) {
                SetValidateInfoItem item = convertToSetValidateInfoItem(ksrcxx);
                hyxxList.add(item);
                ksrcbhList.add(ksrcxx.getKsrcbh());
            }

            // 2. 构建考试信息
            ValidateExamDO exam = new ValidateExamDO();
            exam.setExamPlanCode(ksjhbh);
            exam.setExamSeqCode(ccm);

            // 3. 构建请求DTO
            SetValidateInfoDTO dataDTO = new SetValidateInfoDTO();
            dataDTO.setHyxx(hyxxList);
            dataDTO.setExam(exam);

            // 4. 构建请求对象（使用真实的设备序列号）
            SetValidateInfoReq request = provincePlatformClient.buildSetValidateInfoRequest(dataDTO, deviceSn);

            // 5. 调用上级平台接口
            Wrapper<Void> response = provincePlatformClient.setValidateInfo(request);

            if (response != null && response.success()) {
                log.info("设备{}批次数据上传成功，数量：{}", deviceSn, batchData.size());

                updateKsrcxxSbzt(ksrcbhList);
                return true;
            } else {
                log.error("设备{}批次数据上传失败，响应：{}", deviceSn, response);
                return false;
            }

        } catch (Exception e) {
            log.error("设备{}批次数据上传异常", deviceSn, e);
            return false;
        }
    }

    /**
     * 将KsKsrcxx对象转换为SetValidateInfoItem对象
     * @param ksrcxx 考生入场信息
     * @return 转换后的对象
     */
    private SetValidateInfoItem convertToSetValidateInfoItem(KsKsrcxx ksrcxx) {
        SetValidateInfoItem item = new SetValidateInfoItem();

        // 验证时间 - rcsj
        item.setYzsj(ksrcxx.getRcsj());

        // 核验结果 - 根据sfrc判断：1-通过，0-不通过
        item.setHyjg("1".equals(ksrcxx.getSfrc()) ? "1" : "0");

        // 准考证号
        item.setZkzh(ksrcxx.getZkzh());

        // 考点代码
        item.setKddm(ksrcxx.getKdbh());

        // 考场号
        item.setKch(ksrcxx.getKcbh());

        // 解析验证方式和验证结果
        String yzfs = ksrcxx.getYzfs(); // 验证方式
        String yzjg = ksrcxx.getYzjg(); // 验证结果

        // 初始化各项验证结果为"0"（不通过）
        item.setSzjg("0"); // 刷证结果
        item.setRlsbjg("0"); // 人脸识别结果
        item.setZwrzjg("0"); // 指纹认证结果

        // 根据验证方式和结果设置各项验证结果
        if (StringUtil.isNotBlank(yzfs) && StringUtil.isNotBlank(yzjg) && yzfs.length() == yzjg.length()) {
            for (int i = 0; i < yzfs.length(); i++) {
                char fsChar = yzfs.charAt(i);
                char jgChar = yzjg.charAt(i);

                // 将验证结果转换：0-不通过，1-通过，2-存疑，3-未通过 -> 0或1
                String result = ("1".equals(String.valueOf(jgChar))) ? "1" : "0";

                switch (fsChar) {
                    case '1': // 身份证验证
                        item.setSzjg(result);
                        break;
                    case '2': // 人脸验证
                        item.setRlsbjg(result);
                        break;
                    case '3': // 指纹验证
                        item.setZwrzjg(result);
                        break;
                }
            }
        }

        // 相似度
        item.setXsd(StringUtil.isNotBlank(ksrcxx.getXsd()) ? ksrcxx.getXsd() : "0");

        // 数据优先级 - 默认填10（普通考场，入场上报）
        item.setSjyxj("10");

        return item;
    }

    /**
     * 上传一批考生人工入场信息到上级平台
     * @param batchData 批次数据
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param deviceSn 设备序列号
     * @return 是否成功
     */
    private boolean uploadManualEntranceBatchToProvince(List<KsKsrcxx> batchData, String ksjhbh, String ccm, String deviceSn) {
        try {
            // 1. 转换数据格式
            List<SetValidateStuEnterItem> rcxxList = new ArrayList<>();
            List<String> ksrcbhList = new ArrayList<>();
            for (KsKsrcxx ksrcxx : batchData) {
                SetValidateStuEnterItem item = convertToSetValidateStuEnterItem(ksrcxx);
                rcxxList.add(item);
                ksrcbhList.add(ksjhbh);
            }

            // 2. 构建考试信息
            ValidateExamDO exam = new ValidateExamDO();
            exam.setExamPlanCode(ksjhbh);
            exam.setExamSeqCode(ccm);

            // 3. 构建请求DTO
            SetValidateStuEnterDTO dataDTO = new SetValidateStuEnterDTO();
            dataDTO.setRcxx(rcxxList);
            dataDTO.setExam(exam);

            // 4. 构建请求对象
            SetValidateStuEnterReq request = provincePlatformClient.buildSetValidateStuEnterRequest(dataDTO, deviceSn);

            // 5. 调用上级平台接口
            Wrapper<Void> response = provincePlatformClient.setValidateStuEnter(request);

            if (response != null && response.success()) {
                log.info("设备{}人工入场批次数据上传成功，数量：{}", deviceSn, batchData.size());

                updateKsrcxxSbzt(ksrcbhList);
                return true;
            } else {
                log.error("设备{}人工入场批次数据上传失败，数量：{}，响应：{}", deviceSn, batchData.size(), response);
                return false;
            }

        } catch (Exception e) {
            log.error("设备{}人工入场批次数据上传异常，数量：{}", deviceSn, batchData.size(), e);
            return false;
        }
    }

    private void updateKsrcxxSbzt(List<String> ksrcbhList) {
        if(!CollectionUtils.isEmpty(ksrcbhList)){
            Example example = new Example(KsKsrcxx.class);
            example.createCriteria().andIn("ksrcbh", ksrcbhList);
            KsKsrcxx ksKsrcxx = new KsKsrcxx();
            ksKsrcxx.setSbzt(SbztwEnum.YSB.getCode());
            modelMapper.updateByExampleSelective(ksKsrcxx, example);
        }
    }

    /**
     * 转换KsKsrcxx为SetValidateStuEnterItem
     * @param ksrcxx 考生入场信息
     * @return SetValidateStuEnterItem
     */
    private SetValidateStuEnterItem convertToSetValidateStuEnterItem(KsKsrcxx ksrcxx) {
        SetValidateStuEnterItem item = new SetValidateStuEnterItem();

        // 设备时间 -> 入场时间
        if (ksrcxx.getRcsj() != null) {
//            item.setSbsj(DateUtil.formatDateTime(ksrcxx.getRcsj()));// 设备时间 -> 入场时间
            if (StringUtil.isNotBlank(ksrcxx.getRcsj())) {
                try {
                    Date date = DateUtil.parseDateTime(ksrcxx.getRcsj());
                    item.setSbsj(DateUtil.formatDateTime(date));
                } catch (Exception e) {
                    item.setSbsj(ksrcxx.getRcsj());
                }
            }
        }

        // 准考证号
        item.setZkzh(ksrcxx.getZkzh());

        // 考点代码 -> 考点编号
        item.setKddm(ksrcxx.getKdbh());

        // 考场号
        item.setKch(ksrcxx.getKcbh());

        // 入场状态：根据rgyzjg字段判断
        if (ksrcxx.getRgyzjg() != null) {
            if ("1".equals(ksrcxx.getRgyzjg())) {
                item.setRczt("1"); // 人工审核通过
            } else if ("0".equals(ksrcxx.getRgyzjg())) {
                item.setRczt("0"); // 人工审核不通过
            } else {
                item.setRczt("2"); // 缺考
            }
        } else {
            item.setRczt("2"); // rgyzjg为null时，设为缺考
        }

        // 相似度
        item.setXsd(StringUtil.isNotBlank(ksrcxx.getXsd()) ? ksrcxx.getXsd() : "0");

        // 数据优先级：10-普通考场入场上报
        item.setSjyxj("10");

        return item;
    }

    private List<String> exportKsrcqkxqPhoto(String folderPath, String ksjhbh, String ccm, Map<String, String> fileIdToNameMap, Map<String, String> fileIdToPathMap, List<KsrcqkDatasItemVO> ksrcqkxqcxVOList) {
        List<String> photoFiles = new ArrayList<>();

        List<String> photeFileIds = new ArrayList<>();
        List<String> ksh = new ArrayList<>();
        if(!CollectionUtils.isEmpty(ksrcqkxqcxVOList)){
            ksh = ksrcqkxqcxVOList.stream().map(KsrcqkDatasItemVO::getKsh).collect(Collectors.toList());
        }

        Example em = new Example(KsKsrcxx.class);
        em.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        if (StringUtil.isNotBlank(ccm)){
            em.and().andEqualTo("ccm", ccm);
        }
        if (!CollectionUtils.isEmpty(ksh)){
            em.and().andIn("ksh", ksh);
        }
        List<KsKsrcxx> ksKsrcxxes = this.selectListByExample(em);
//        List<String> ksh = ksKsrcxxes.stream().map(KsKsrcxx::getKsh).collect(Collectors.toList());
        Example ksbmEg = new Example(KsBmxx.class);
        ksbmEg.createCriteria()
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhid", ksjhbh)
                .andIn("ksh", ksh);
//        ksbmEg.selectProperties("zp");
        List<KsBmxx> ksBmxxes = ksBmxxService.selectListByExample(ksbmEg);


        Map<String, String> kshZhzhMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ksKsrcxxes)) {
            for (KsKsrcxx ksKsrcxx : ksKsrcxxes) {
                kshZhzhMap.put(ksKsrcxx.getKsh(), ksKsrcxx.getZkzh());
                String xczp = ksKsrcxx.getRcrlzp();
                if (StringUtil.isNotBlank(xczp) && isShuaLian(ksKsrcxx)) {
                    fileIdToNameMap.put(xczp, StringUtil.join( File.separator, "现场照片", ksKsrcxx.getCcm(), StringUtil.join("_", ksKsrcxx.getCcm(), ksKsrcxx.getZkzh(), "xczp", ".jpg")) );
                    photeFileIds.add(xczp);
                }
                String sfzzp = ksKsrcxx.getSfzp();
                if (StringUtil.isNotBlank(sfzzp)) {
                    fileIdToNameMap.put(sfzzp, StringUtil.join(File.separator, "身份证照片", ksKsrcxx.getCcm(), StringUtil.join("_", ksKsrcxx.getCcm(), ksKsrcxx.getZkzh(), "sfzzp", ".jpg")) );
                    photeFileIds.add(sfzzp);
                }
            }
        }
        if (!CollectionUtils.isEmpty(ksBmxxes)) {
            for (KsBmxx ksBmxx : ksBmxxes) {
                String zp = ksBmxx.getZp();
                if (StringUtil.isNotBlank(zp)) {
                    fileIdToNameMap.put(zp, StringUtil.join(File.separator, "报名照片" , StringUtil.join("_", kshZhzhMap.getOrDefault(ksBmxx.getKsh(), ksBmxx.getKsh()), "bmzp", ".jpg"))) ;
                    photeFileIds.add(zp);
                }
            }
        }

        GetAttachUrlDTO getAttachUrlDTO = new GetAttachUrlDTO();
        getAttachUrlDTO.setFileIds(photeFileIds);
        GetAttachUrlVO attachUrl = attachmentHandler.getAttachUrl(getAttachUrlDTO);
        for (AttachFileVoItemVO attachFileVoItemVO : attachUrl.getAttachFileVoList()) {
            fileIdToPathMap.put(attachFileVoItemVO.getFile(), attachFileVoItemVO.getUrl());
        }

        return photeFileIds;
    }

    private boolean isShuaLian(KsKsrcxx ksKsrcxx) {
        if(!StringUtil.equals(SfhySftgEnum.YRC.getCode(), ksKsrcxx.getSfrc())){
            return false;
        }
        if (StringUtil.isBlank(ksKsrcxx.getRgyzjg())){
            return true;
        }
        return false;
    }

    // 递归删除目录方法
    public static void deleteDirectoryRecursively(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            return;
        }
        Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.deleteIfExists(file);
                return FileVisitResult.CONTINUE;
            }
            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.deleteIfExists(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }
    private String exportKsrcqkxqExcel(String folderPath, List<KsrcqkDatasItemVO> ksrcqkxqcxVOList) {
        List<KsrcqkxqTemplate> collect = ksrcqkxqcxVOList.stream().map(item -> {
            KsrcqkxqTemplate ksrcqkxqTemplate = new KsrcqkxqTemplate();
            ksrcqkxqTemplate.setCcmc(item.getCcmc());
            ksrcqkxqTemplate.setKch(item.getKcbh());
            ksrcqkxqTemplate.setZwh(item.getZwh());
            ksrcqkxqTemplate.setKsxm(item.getKsxm());
            ksrcqkxqTemplate.setZkzh(item.getZkzh());
            String sbfs = convertSbfs(item.getSbfs());
            ksrcqkxqTemplate.setSbfs(sbfs);
            String sfrc = convertSfrc(item.getSfrc());
            ksrcqkxqTemplate.setRczt(sfrc);
            ksrcqkxqTemplate.setRcsj(item.getRcsj());
            ksrcqkxqTemplate.setXsd(item.getXsd());
            return ksrcqkxqTemplate;
        }).collect(Collectors.toList());
        String fileName = folderPath+ File.separator + "考生入场信息" + DateUtil.getCurrentDate("yyyy-MM-dd_HHmmss") + ".xlsx";
        // 按场次分组,写到同一个文件的不同sheet
        Map<String, List<KsrcqkxqTemplate>> groupedData = collect.stream()
                .collect(Collectors.groupingBy(KsrcqkxqTemplate::getCcmc));
//        EasyExcel.write(fileName, KsrcqkxqTemplate.class).sheet().doWrite(collect);
        // 创建ExcelWriter
        ExcelWriter build = EasyExcel.write(fileName, KsrcqkxqTemplate.class).build();
        // 遍历分组数据
        for (Map.Entry<String, List<KsrcqkxqTemplate>> entry : groupedData.entrySet()) {
            String sheetName = entry.getKey();
            List<KsrcqkxqTemplate> dataList = entry.getValue();
            // 创建Sheet
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
            // 写入数据
            build.write(dataList, writeSheet);
        }
        build.finish();
        return fileName;
    }

    private String convertSfrc(String sfrc) {
//        0缺考/1已入场/9待确定
        if (StringUtils.isBlank(sfrc)) {
            return "";
        }
        if (StringUtil.equals(sfrc, "0")) {
            return "缺考";
        } else if (StringUtil.equals(sfrc, "1")) {
            return "已入场";
        } else if (StringUtil.equals(sfrc, "9")) {
            return "待确定";
        } else {
            return "";
        }
    }

    private String convertSbfs(String sbfs) {
        // 上报方式 0 刷脸 1 手动
        if (StringUtils.isBlank(sbfs)) {
            return "";
        }
        if (StringUtil.equals(sbfs, "0")) {
            return "刷脸";
        } else if (StringUtil.equals(sbfs, "1")) {
            return "手动";
        } else {
            return "";
        }
    }


    private void sendDataMissingToKc(String ksjhbh, String ccm, List<KsKcxx> kdKcxxes) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(kdKcxxes)) {
            return;
        }
        List<String> kcidList = kdKcxxes.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());
        Example emDistribute = new Example(KsKssjDistributeStatus.class);
        emDistribute.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("type", "1")
                .andIn("bzhkcbh", kcidList);
        emDistribute.selectProperties("bzhkcbh", "ydzdDownloadSn");
        emDistribute.setDistinct(true);
        List<KsKssjDistributeStatus> ksDistributeStatuses = ksKssjDistributeStatusService.selectListByExample(emDistribute);
        Map<String, String> kczdXlhMap = new HashMap<>(); // 考场移动终端sn映射
        for (KsKssjDistributeStatus ksDistributeStatus : ksDistributeStatuses) {
            kczdXlhMap.put(ksDistributeStatus.getBzhkcbh(), ksDistributeStatus.getYdzdDownloadSn());
        }

        for (KsKcxx kdKcxx : kdKcxxes) {
            JSONArray qssjArray = new JSONArray();
            Example em = new Example(KsKsrcxx.class);
            em.selectProperties("zkzh", "kcbh", "ljkcbh");
            em.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("ccm", ccm)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                    .andEqualTo("bzhkdid", kdKcxx.getBzhkdid())
                    .andEqualTo("kcbh", kdKcxx.getKcbh())
                    .andEqualTo("sfrc", "9");
            List<KsKsrcxx> list = modelMapper.selectByExample(em);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                // 准考证号按kcbh_ljkcbh分组
                Map<String, List<String>> zkzhMap = new HashMap<>();
                for (KsKsrcxx ksKsrcxx : list) {
                    String key = ksKsrcxx.getKcbh() + "_" + ksKsrcxx.getLjkcbh();
                    zkzhMap.putIfAbsent(key, new ArrayList<>());
                    zkzhMap.get(key).add(ksKsrcxx.getZkzh());
                }

                zkzhMap.forEach((kcbh_ljkcbh, zkzhList) -> {
                    String[] kcbh_ljkcbhArr = kcbh_ljkcbh.split("_");
                    String kcbh = kcbh_ljkcbhArr[0];
                    String ljkcbh = kcbh_ljkcbhArr[1];
                    JSONObject dataJson = buildQssj(zkzhList, ksjhbh, ccm, kdKcxx.getBzhkdid(), kcbh, ljkcbh);
                    qssjArray.add(dataJson);
                });

                unifyAccessUtils.sendQssj(kczdXlhMap.get(kdKcxx.getBzhkcid()), qssjArray);

            }
        }
    }

    private JSONObject buildQssj(List<String> zkzhList, String ksjhbh, String ccm, String bzhkdid, String kcbh, String ljkcbh) {
        JSONObject json = new JSONObject();
        json.put("ksbh", ksjhbh);
        json.put("ccm", ccm);
        json.put("kddm", bzhkdid);
        json.put("kch", kcbh);
        json.put("ljkcbh", ljkcbh);
        JSONArray jsonArray = new JSONArray();
        for (String zkzh : zkzhList) {
            JSONObject mjson = new JSONObject();
            mjson.put("zkzh", zkzh);
            jsonArray.add(mjson);
        }
        json.put("kslb", jsonArray);
        return json;
    }

    @Override
    public UploadValidateInfoResultVO uploadCandidatePhotosToProvince(String ksjhbh, String ccm) {
        log.info("开始上传考生照片到上级平台，考试计划编号：{}，场次码：{}", ksjhbh, ccm);

        UploadValidateInfoResultVO result = new UploadValidateInfoResultVO();
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setFailCount(0);
        result.setBatchCount(0);
        result.setAllSuccess(true);

        try {

            // 2. 统计总数
            int totalCount = modelMapper.countForPhotoUploadToProvince(ksjhbh, ccm);
            result.setTotalCount(totalCount);

            if (totalCount == 0) {
                result.setMessage("没有需要上传的考生照片信息");
                log.info("没有需要上传的考生照片信息，考试计划编号：{}，场次码：{}", ksjhbh, ccm);
                return result;
            }

            log.info("共找到{}条考生照片信息需要上传", totalCount);

            // 3. 分批处理，每批50条
            int batchSize = 50;
            int batchCount = (totalCount + batchSize - 1) / batchSize;
            result.setBatchCount(batchCount);

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (int i = 0; i < batchCount; i++) {
                int offset = i * batchSize;
                log.info("开始处理第{}批照片数据，偏移量：{}，批次大小：{}", i + 1, offset, batchSize);

                try {
                    // 查询当前批次的数据
                    List<KsKsrcxx> batchData = modelMapper.selectForPhotoUploadToProvince(ksjhbh, ccm, offset, batchSize);

                    if (CollectionUtils.isEmpty(batchData)) {
                        log.warn("第{}批照片数据为空，跳过", i + 1);
                        continue;
                    }

                    // 按设备序列号分组
                    Map<String, List<KsKsrcxx>> deviceGroupMap = batchData.stream()
                            .collect(Collectors.groupingBy(ksrcxx ->
                                StringUtil.isNotBlank(ksrcxx.getSbxlh()) ? ksrcxx.getSbxlh() : "DEFAULT_DEVICE_SN"));

                    // 按设备序列号分组上传
                    boolean allBatchSuccess = true;
                    for (Map.Entry<String, List<KsKsrcxx>> entry : deviceGroupMap.entrySet()) {
                        String deviceSn = entry.getKey();
                        List<KsKsrcxx> deviceData = entry.getValue();

                        log.info("第{}批照片数据中设备{}的数据量：{}", i + 1, deviceSn, deviceData.size());

                        boolean deviceBatchSuccess = uploadPhotoBatchToProvince(deviceData, ksjhbh, ccm, deviceSn);

                        if (!deviceBatchSuccess) {
                            allBatchSuccess = false;
                            log.error("第{}批照片数据中设备{}上传失败，数量：{}", i + 1, deviceSn, deviceData.size());
                        } else {
                            log.info("第{}批照片数据中设备{}上传成功，数量：{}", i + 1, deviceSn, deviceData.size());
                        }
                    }

                    if (allBatchSuccess) {
                        successCount += batchData.size();
                        log.info("第{}批照片数据上传成功，数量：{}", i + 1, batchData.size());
                    } else {
                        failCount += batchData.size();
                        result.setAllSuccess(false);
                        errorMessages.append("第").append(i + 1).append("批照片数据上传失败；");
                        log.error("第{}批照片数据上传失败，数量：{}", i + 1, batchData.size());
                    }

                } catch (Exception e) {
                    failCount += Math.min(batchSize, totalCount - offset);
                    result.setAllSuccess(false);
                    errorMessages.append("第").append(i + 1).append("批照片数据处理异常：").append(e.getMessage()).append("；");
                    log.error("第{}批照片数据处理异常", i + 1, e);
                }
            }

            result.setSuccessCount(successCount);
            result.setFailCount(failCount);

            if (result.getAllSuccess()) {
                result.setMessage("所有照片数据上传成功");
            } else {
                result.setMessage("部分照片数据上传失败：" + errorMessages.toString());
            }

            log.info("考生照片上传完成，总数：{}，成功：{}，失败：{}，批次数：{}",
                    totalCount, successCount, failCount, batchCount);

        } catch (Exception e) {
            result.setAllSuccess(false);
            result.setMessage("上传过程中发生异常：" + e.getMessage());
            log.error("上传考生照片到上级平台异常", e);
        }

        return result;
    }

    /**
     * 上传一批考生照片到上级平台
     * @param batchData 批次数据
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param deviceSn 设备序列号
     * @return 是否成功
     */
    private boolean uploadPhotoBatchToProvince(List<KsKsrcxx> batchData, String ksjhbh, String ccm, String deviceSn) {
        try {
            // 1. 转换数据格式并处理照片上传
            List<SetValidatePicItem> photoList = new ArrayList<>();
            List<KsKsrcxx> updatedRecords = new ArrayList<>();

            for (KsKsrcxx ksrcxx : batchData) {
                SetValidatePicItem item = new SetValidatePicItem();
                item.setZkzh(ksrcxx.getZkzh());
                item.setKddm(ksrcxx.getKdbh());
                item.setKch(ksrcxx.getKcbh());

                // 处理身份证照片
                String sfzpId = null;
                if (StringUtil.isNotBlank(ksrcxx.getSfzp())) {
                    sfzpId = upperHsDfsHandler.uploadPhotoToUpperPlatform(ksrcxx.getSfzp(), "sfzp_" + ksrcxx.getZkzh() + ".jpg");
                }

                // 处理人脸照片
                String rcrlzpId = null;
                if (StringUtil.isNotBlank(ksrcxx.getRcrlzp())) {
                    rcrlzpId = upperHsDfsHandler.uploadPhotoToUpperPlatform(ksrcxx.getRcrlzp(), "rcrlzp_" + ksrcxx.getZkzh() + ".jpg");
                }

                // 只有成功上传的照片才添加到上传列表
                if (StringUtil.isNotBlank(sfzpId) || StringUtil.isNotBlank(rcrlzpId)) {
                    item.setSfzzp(sfzpId);
                    item.setXczp(rcrlzpId);
                    photoList.add(item);

                    // 更新数据库记录
                    KsKsrcxx updatedRecord = new KsKsrcxx();
                    updatedRecord.setKsrcbh(ksrcxx.getKsrcbh());
                    updatedRecord.setScSfzp(sfzpId);
                    updatedRecord.setScRlzp(rcrlzpId);
                    updatedRecord.setTbzt("1");
                    updatedRecords.add(updatedRecord);
                }
            }

            if (photoList.isEmpty()) {
                log.warn("设备{}批次数据中没有可上传的照片", deviceSn);
                return false;
            }

            // 2. 构建考试信息
            ValidateExamDO exam = new ValidateExamDO();
            exam.setExamPlanCode(ksjhbh);
            exam.setExamSeqCode(ccm);

            // 3. 构建请求DTO
            SetValidatePicDTO dataDTO = new SetValidatePicDTO();
            dataDTO.setHyxx(photoList);
            dataDTO.setExam(exam);

            // 4. 构建请求对象
            SetValidatePicReq request = provincePlatformClient.buildSetValidatePicRequest(dataDTO, deviceSn);

            // 5. 调用上级平台接口
            Wrapper<Void> response = provincePlatformClient.setValidatePic(request);

            if (response != null && response.success()) {
                // 6. 更新数据库中的上传状态
                if (!updatedRecords.isEmpty()) {
                    modelMapper.batchUpdatePhotoUploadStatus(updatedRecords);
                }
                log.info("设备{}照片批次数据上传成功，数量：{}", deviceSn, photoList.size());
                return true;
            } else {
                log.error("设备{}照片批次数据上传失败，数量：{}，响应：{}", deviceSn, photoList.size(), response);
                return false;
            }

        } catch (Exception e) {
            log.error("设备{}照片批次数据上传异常，数量：{}", deviceSn, batchData.size(), e);
            return false;
        }
    }
}

