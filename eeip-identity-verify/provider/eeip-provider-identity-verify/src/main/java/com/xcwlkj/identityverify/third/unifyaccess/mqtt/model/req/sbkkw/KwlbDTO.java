package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class KwlbDTO {
    /** 逻辑考场号（考点唯一） */
    private String ljkch;

    /** 考场号 */
    private String kch;

    /** 考生座位详情 */
    private List<KzwhxqDTO> kzwhxq;
}
