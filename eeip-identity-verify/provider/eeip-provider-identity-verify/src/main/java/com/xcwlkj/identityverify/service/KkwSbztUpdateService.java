package com.xcwlkj.identityverify.service;

import java.util.Date;
import java.util.List;

/**
 * 空考位上报状态更新服务
 * <AUTHOR>
 * @version $Id: KkwSbztUpdateService.java, v 0.1 2025年07月30日 xcwlkj.com Exp $
 */
public interface KkwSbztUpdateService {

    /**
     * 批量更新空考位上报状态
     * @param ids 空考位消息ID列表
     * @param sbzt 上报状态 0-未上报 1-已上报 -1-上报失败
     * @param sbsj 上报时间
     */
    void batchUpdateSbzt(List<String> ids, String sbzt, Date sbsj);

    /**
     * 根据条件更新空考位上报状态
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @param ljkcbh 逻辑考场编号
     * @param sbzt 上报状态
     * @param sbsj 上报时间
     */
    void updateSbztByCondition(String ksjhbh, String ccm, String ljkcbh, String sbzt, Date sbsj);

    /**
     * 查询未上报的空考位数据
     * @param ksjhbh 考试计划编号
     * @param ccm 场次码
     * @return 未上报的空考位消息列表
     */
    List<String> findUnreportedKkwIds(String ksjhbh, String ccm);
}
