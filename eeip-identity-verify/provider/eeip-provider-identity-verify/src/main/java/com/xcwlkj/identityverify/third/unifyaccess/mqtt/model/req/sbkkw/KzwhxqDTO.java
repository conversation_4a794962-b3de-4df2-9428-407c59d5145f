package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class KzwhxqDTO {
    /** 准考证号 */
    private String zkzh;

    /** 编排座位号 */
    private String bpzwh;

    /** 实际座位号 */
    private String sjzwh;

    /**
     * 入场备注
     * 1-误识别 2-坐错他人位置 3-实际未参加考试
     * 4-他人坐错位置 5-人工核验 6-缺考（空位）7-无编排
     */
    private String rcbz;

    /**
     * 数据优先级
     * 10-普通考场（入场上报）
     * 20-考场空位上报
     * 30-备用考场上报入场
     */
    private Integer sjyxj;

    /** 核验信息 */
    private YsbhyxxDTO ysbhyxx;

    /** 入场信息 */
    private YsbrcxxDTO ysbrcxx;
}
