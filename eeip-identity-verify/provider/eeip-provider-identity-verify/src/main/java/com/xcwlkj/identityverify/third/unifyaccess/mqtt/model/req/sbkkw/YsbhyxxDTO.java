package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class YsbhyxxDTO {
    /** 验证时间 */
    private String yzsj;

    /** 核验结果 1-通过 0-不通过 */
    private String hyjg;

    /** 考点代码 */
    private String kddm;

    /** 刷证结果 1-通过 0-不通过 */
    private String szjg;

    /** 人脸识别结果 1-通过 0-不通过 */
    private String rlsbjg;

    /** 指纹认证结果 1-通过 0-不通过 */
    private String zwrzjg;
}
