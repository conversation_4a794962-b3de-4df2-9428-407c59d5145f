package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.identityverify.service.impl.MqttSbkkwEventDbHandler;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.StringOperationReq;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw.SbkkwReq;
import com.xcwlkj.identityverify.util.SecretUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

//	"Data": {
//           "dataArray":[{
//		     "examPlanCode": "考试编号",
//		     "orgCode": "机构编号",
//                "sn": "设备序列号",
//,"devType":"172"//设备类型见附录
//,"ccm":"场次码"
//,"bzhkcid":"标准化考场id"
//,"zcqswzm":"座次起始位置码"
//,"zwbjfsm":"座位布局方式码"
//,"zwplfsm":"座位排列方式码"
//,"kwlb":[
//{
//"ljkch":"逻辑考场号考点唯一"
//,"kch":"考场号"
//,"kzwhxq":[{
//   "zkzh":"准考证号"
//    ,"bpzwh":"编排数据中考生座位号"
//,"sjzwh":"实际座位号"
//,"rcbz":"入场备注1-误识别 2-坐错他人位置 3-实际未参加考试 4-他人坐错位置 5-人工核验 6-缺考（空位）7-无编排"
//,"sjyxj":"数据优先级 10-普通考场（入场上报）20-考场空位上报 30-备用考场上报入场"
//,"ysbhyxx":{
//"yzsj": "验证时间yyyy-MM-dd HH:mm:ss",
//"hyjg": "核验结果 1-通过 0-不通过",
//"kddm": "考点代码",
//"szjg": "刷证结果 1-通过 0-不通过",
//"rlsbjg": "人脸识别结果 1-通过 0-不通过",
//"zwrzjg": "指纹认证结果 1-通过 0-不通过"
//}
//,"ysbrcxx":{
//"sbsj": "上报时间yyyy-MM-dd HH:mm:ss",
//"kddm": "考点代码",
//"rczt": " 入场状态 0-人工审核不通过，1-人工审核通过，2-缺考"
// }
//}]
//}
//]
//          ,"czsj":"操作时间yyyy-MM-dd HH:mm:ss"
//        }]
//
// }

/**
 * 处理上报空考位
 */
@Slf4j
@Service
public class MqttSbkkwEventHandler extends MqttBaseEventHandler{
    @Resource
    private SecretUtils secretUtils;
    @Resource
    private MqttSbkkwEventDbHandler sbkkwEventDbHandler;
    @Override
    protected HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        HanleResult hanleResult = new HanleResult();
        Object respObj = null;
        boolean needToSendResp = false;
        switch (devEventEnum){
            case SBKKW:
                // 解密数据
                StringOperationReq stringOperationReq = (StringOperationReq) req;
                if(StringUtils.isBlank(stringOperationReq.getSbxlh())){
                    stringOperationReq.setSbxlh(deviceId);
                }
                JSONObject data = secretUtils.decryptData(stringOperationReq);
                Long timestamp = stringOperationReq.getTimestamp();
                sbkkwEventDbHandler.sbkkw(data,deviceId,timestamp);
                break;
            default:
                break;
        }
        return hanleResult;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.SBKKW_EVENT_HANDLER;
    }
}
