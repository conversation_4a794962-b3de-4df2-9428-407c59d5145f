package com.xcwlkj.identityverify.third.superior.http.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum HisomeUriEnum {
    GET_TOKEN("/manager/getConnToken", "获取token"),
    GET_ACCESS_TOKEN("/manager/secret/getAccessToken", "获取access token"),
    GET_EXAM_PLAN("/ksrc/examInfo/getExamPlan/v1", "获取考试计划"),
    GET_EXAM_ARRG_INFO("/ksrc/examInfo/getExamArrgInfo/v2", "获取文件地址"),
    SET_VALIDATE_INFO_NEW("/ksyw/revIdvData/setValidateInfo", "考生核验信息"),
    SET_VALIDATE_STU_ENTER_NEW("/ksyw/revIdvData/setValidateStuEnter", "考场入场人工（入场、缺考）"),
    SET_VALIDATE_PIC("/ksyw/revIdvData/setValidatePic", "考生照片上传"),
    PKG_DOWNLOAD_COMPLETE("/ksrc/examInfo/pkgDownloadComplete/v1", "完成编排数据下载上报"),
    JKRYQD("/ksyw/revIdvData/jkryQd", "监考人员签到"),
    JKQDZP("/ksyw/revIdvData/jkqdZp", "监考照片上传"),
    SBKKW("/ksyw/revIdvData/sbkkw", "空考位上报"),
    KSRCZS("/ksyw/revIdvData/ksrczs", "考生入场总数");

    private String url;

    private String name;
}
