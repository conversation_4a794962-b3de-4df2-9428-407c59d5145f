package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.BaseOperationReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SbkkwReq extends BaseOperationReq<SbkkwReq> {

    /**
     * 业务数据数组
     */
    private List<SbkkwItemDTO> dataArray;
}
