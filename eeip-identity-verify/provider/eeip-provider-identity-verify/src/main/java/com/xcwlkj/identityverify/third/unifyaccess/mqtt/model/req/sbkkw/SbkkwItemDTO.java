package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SbkkwItemDTO {
    /** 考试计划编号 */
    private String examPlanCode;
    /** 机构编号 */
    private String orgCode;

    /** 设备序列号 */
    private String sn;

    /** 设备类型（见附录） */
    private String devType;

    /** 场次码 */
    private String ccm;

    /** 标准化考场 ID */
    private String bzhkcid;

    /** 座次起始位置码 */
    private String zcqswzm;

    /** 座位布局方式码 */
    private String zwbjfsm;

    /** 座位排列方式码 */
    private String zwplfsm;

    /** 考务列表 */
    private List<KwlbDTO> kwlb;

    /** 操作时间 */
    private String czsj;

}
