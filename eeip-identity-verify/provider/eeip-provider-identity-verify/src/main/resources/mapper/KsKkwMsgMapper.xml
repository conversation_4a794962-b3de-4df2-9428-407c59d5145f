<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.identityverify.mapper.KsKkwMsgMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.identityverify.model.domain.KsKkwMsg">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="KSJHBH" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="CCM" jdbcType="VARCHAR" property="ccm" />
        <result column="BZHKDID" jdbcType="VARCHAR" property="bzhkdid" />
        <result column="BZHKCID" jdbcType="VARCHAR" property="bzhkcid" />
        <result column="SN" jdbcType="VARCHAR" property="sn" />
        <result column="SCZT" jdbcType="VARCHAR" property="sczt" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="KS_ZKZH" jdbcType="VARCHAR" property="ksZkzh" />
        <result column="KS_BPZWH" jdbcType="VARCHAR" property="ksBpzwh" />
        <result column="KS_SJZWH" jdbcType="VARCHAR" property="ksSjzwh" />
        <result column="KS_KKW" jdbcType="VARCHAR" property="ksKkw" />
        <result column="ZCQSWZM" jdbcType="VARCHAR" property="zcqswzm" />
        <result column="ZWBJFSM" jdbcType="VARCHAR" property="zwbjfsm" />
        <result column="ZWPLFSM" jdbcType="VARCHAR" property="zwplfsm" />
        <result column="LJKCH" jdbcType="VARCHAR" property="ljkch" />
        <result column="KCH" jdbcType="VARCHAR" property="kch" />
        <result column="DEV_TYPE" jdbcType="VARCHAR" property="devType" />
        <result column="RCBZ" jdbcType="VARCHAR" property="rcbz" />
        <result column="SJYXJ" jdbcType="INTEGER" property="sjyxj" />
        <result column="TIMESTAMP" jdbcType="TIMESTAMP" property="timestamp" />
        <result column="YZFS" jdbcType="VARCHAR" property="yzfs" />
        <result column="YZJG" jdbcType="VARCHAR" property="yzjg" />
        <result column="SFRC" jdbcType="VARCHAR" property="sfrc" />
        <result column="RCSJ" jdbcType="VARCHAR" property="rcsj" />
        <result column="RCSJFZ" jdbcType="VARCHAR" property="rcsjfz" />
        <result column="RGYZJG" jdbcType="VARCHAR" property="rgyzjg" />
        <result column="SJLY" jdbcType="VARCHAR" property="sjly" />
        <result column="CZSJ" jdbcType="TIMESTAMP" property="czsj" />
	</resultMap>

	<!-- 列信息 -->
	<sql id="Base_Column_List">
        ID,
        KSJHBH,
        CCM,
        BZHKDID,
        BZHKCID,
        SN,
        SCZT,
        CREATE_TIME,
        UPDATE_TIME,
        KS_ZKZH,
        KS_BPZWH,
        KS_SJZWH,
        KS_KKW,
        ZCQSWZM,
        ZWBJFSM,
        ZWPLFSM,
        LJKCH,
        KCH,
        DEV_TYPE,
        RCBZ,
        SJYXJ,
        TIMESTAMP,
        YZFS,
        YZJG,
        SFRC,
        RCSJ,
        RCSJFZ,
        RGYZJG,
        SJLY,
        CZSJ
	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND ID = #{id,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND KSJHBH = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND CCM = #{ccm,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdid != null and bzhkdid != ''">
            AND BZHKDID = #{bzhkdid,jdbcType=VARCHAR}
        </if>
        <if test="bzhkcid != null and bzhkcid != ''">
            AND BZHKCID = #{bzhkcid,jdbcType=VARCHAR}
        </if>
        <if test="sn != null and sn != ''">
            AND SN = #{sn,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND SCZT = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="ksZkzh != null and ksZkzh != ''">
            AND KS_ZKZH = #{ksZkzh,jdbcType=VARCHAR}
        </if>
        <if test="ksBpzwh != null and ksBpzwh != ''">
            AND KS_BPZWH = #{ksBpzwh,jdbcType=VARCHAR}
        </if>
        <if test="ksSjzwh != null and ksSjzwh != ''">
            AND KS_SJZWH = #{ksSjzwh,jdbcType=VARCHAR}
        </if>
        <if test="ksKkw != null and ksKkw != ''">
            AND KS_KKW = #{ksKkw,jdbcType=VARCHAR}
        </if>
        <if test="ljkch != null and ljkch != ''">
            AND LJKCH = #{ljkch,jdbcType=VARCHAR}
        </if>
        <if test="kch != null and kch != ''">
            AND KCH = #{kch,jdbcType=VARCHAR}
        </if>
        <if test="devType != null and devType != ''">
            AND DEV_TYPE = #{devType,jdbcType=VARCHAR}
        </if>
        <if test="rcbz != null and rcbz != ''">
            AND RCBZ = #{rcbz,jdbcType=VARCHAR}
        </if>
        <if test="sjyxj != null">
            AND SJYXJ = #{sjyxj,jdbcType=INTEGER}
        </if>
        <if test="yzfs != null and yzfs != ''">
            AND YZFS = #{yzfs,jdbcType=VARCHAR}
        </if>
        <if test="yzjg != null and yzjg != ''">
            AND YZJG = #{yzjg,jdbcType=VARCHAR}
        </if>
        <if test="sfrc != null and sfrc != ''">
            AND SFRC = #{sfrc,jdbcType=VARCHAR}
        </if>
        <if test="sjly != null and sjly != ''">
            AND SJLY = #{sjly,jdbcType=VARCHAR}
        </if>
	</sql>

    <!-- 空考位统计列表查询 -->
    <select id="selectKkwTjLb" resultType="com.xcwlkj.identityverify.model.vo.cxtj.KkwTjItem">
        SELECT DISTINCT
            kc.kcbh as kch,
            COALESCE(kc.bzhkcmc, js.jsmc) as csmc,
            CASE
                WHEN COUNT(msg.id) > 0 THEN '1'
                ELSE '0'
            END as sfsb,
            DATE_FORMAT(MAX(msg.czsj), '%Y-%m-%d %H:%i:%s') as sbsj,
            kc.bzhkcid as bzhkcid,
            kc.ljkcbh as ljkcbh
        FROM ks_kcxx kc
        LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
            AND msg.SCZT = '0'
            AND kc.ksjhbh = msg.KSJHBH
            AND kc.ccm = msg.CCM
            <if test="ccm != null and ccm != ''">
            AND msg.CCM = #{ccm}
            </if>
        LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
        WHERE kc.sczt = '0'
            <if test="ksjhbh != null and ksjhbh != ''">
            AND kc.ksjhbh = #{ksjhbh}
            </if>
            <if test="ccm != null and ccm != ''">
            AND kc.ccm = #{ccm}
            </if>
            <if test="sbzt != null and sbzt != ''">
                <if test="sbzt == '1'.toString()">
                AND msg.id IS NOT NULL
                </if>
                <if test="sbzt == '0'.toString()">
                AND msg.id IS NULL
                </if>
            </if>
        GROUP BY kc.id, kc.kcbh, kc.bzhkcmc, js.jsmc, kc.bzhkcid, kc.ljkcbh
        ORDER BY kc.kcbh
    </select>

    <select id="selectKkwTjInfo" resultType="com.xcwlkj.identityverify.model.vo.cxtj.KkwTjInfoVO">
        SELECT
            COUNT(DISTINCT kc.ljkcbh) as total,
            COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) as ysb,
            COUNT(DISTINCT CASE WHEN msg.id IS NULL THEN kc.ljkcbh END) as wsb,
            CONCAT(
                ROUND(
                    COALESCE(COUNT(DISTINCT CASE WHEN msg.id IS NOT NULL THEN kc.ljkcbh END) * 100.0 /
                    NULLIF(COUNT(DISTINCT kc.ljkcbh), 0), 0),
                    2
                ),
                '%'
            ) as reportRate
        FROM ks_kcxx kc
        LEFT JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
            AND kc.ksjhbh = msg.KSJHBH
            AND msg.SCZT = '0'
            <if test="ccm != null and ccm != ''">
            AND kc.ccm = msg.CCM
            AND msg.CCM = #{ccm}
            </if>
        WHERE kc.sczt = '0'
            <if test="ksjhbh != null and ksjhbh != ''">
            AND kc.ksjhbh = #{ksjhbh}
            </if>
            <if test="ccm != null and ccm != ''">
            AND kc.ccm = #{ccm}
            </if>
    </select>

    <select id="selectKkwTjXq" resultType="com.xcwlkj.identityverify.model.vo.cxtj.KkwTjXqVO">
        SELECT
            kc.kcbh as kch,
            kc.ljkcbh as ljkcbh,
            js.jsmc as csmc,
            msg.KS_ZKZH as zkzh,
            ksxx.ksxm as xm,
            msg.KS_BPZWH as zwh,
            msg.RCBZ yclx,
            DATE_FORMAT(msg.timestamp, '%Y-%m-%d %H:%i:%s') as zdsbsj,
            msg.report_flag as sfsb,
            DATE_FORMAT(msg.report_time, '%Y-%m-%d %H:%i:%s') as sbsjsj
        FROM ks_kcxx kc
        INNER JOIN ks_kkw_msg msg ON kc.ljkcbh = msg.LJKCH
            AND msg.SCZT = '0'
            AND kc.ksjhbh = msg.KSJHBH
            AND kc.ccm = msg.ccm
        LEFT JOIN ks_ksrcxx ksxx on
        msg.ksjhbh = ksxx.ksjhbh AND
        msg.ccm = ksxx.ccm AND msg.ljkch = ksxx.ljkcbh AND ksxx.zkzh = msg.ks_zkzh
        LEFT JOIN cs_jsjbxx js ON kc.bzhkcid = js.bzhkcid AND js.sczt = '0'
#         LEFT JOIN (
#         select sum(sbkszs) as sbkszs,ksjhbh,ccm,ljkcbh,sblx,max(report_flag) as report_flag,max(report_time) as report_time from ks_ydsb_sbkszs GROUP BY ksjhbh,ccm,ljkcbh,sblx
#         ) sbzs  ON
#         msg.ksjhbh = sbzs.ksjhbh AND msg.ccm = sbzs.ccm AND msg.ljkch = sbzs.ljkcbh

        WHERE kc.sczt = '0'
            <if test="ksjhbh != null and ksjhbh != ''">
            AND kc.ksjhbh = #{ksjhbh}
            </if>
            <if test="ccm != null and ccm != ''">
            AND msg.CCM = #{ccm}
            </if>
            <if test="yclx != null and yclx != ''">
            AND msg.RCBZ = #{yclx}
            </if>
            <if test="zkzh != null and zkzh != ''">
            AND msg.KS_ZKZH LIKE CONCAT('%', #{zkzh}, '%')
            </if>
            <if test="kch != null and kch != ''">
            AND kc.ljkcbh LIKE CONCAT('%', #{kch}, '%')
            </if>
        ORDER BY kc.ljkcbh, msg.KS_BPZWH
    </select>

</mapper>
