package com.xcwlkj.identityverify.service.impl;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.sbkkw.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 空考位上报处理器测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class MqttSbkkwEventDbHandlerTest {

    @Resource
    private MqttSbkkwEventDbHandler mqttSbkkwEventDbHandler;

    /**
     * 测试空考位上报 - 有考生数据
     */
    @Test
    public void testSbkkwWithStudentData() {
        // 构建测试数据
        SbkkwReq sbkkwReq = new SbkkwReq();
        
        List<SbkkwItemDTO> dataArray = new ArrayList<>();
        SbkkwItemDTO sbkkwItem = new SbkkwItemDTO();
        sbkkwItem.setExamPlanCode("TEST_EXAM_001");
        sbkkwItem.setOrgCode("TEST_ORG_001");
        sbkkwItem.setSn("TEST_SN_001");
        sbkkwItem.setDevType("172");
        sbkkwItem.setCcm("001");
        sbkkwItem.setBzhkcid("TEST_BZHKCID_001");
        sbkkwItem.setZcqswzm("01");
        sbkkwItem.setZwbjfsm("01");
        sbkkwItem.setZwplfsm("01");
        sbkkwItem.setCzsj("2025-01-15 10:30:00");
        
        // 构建考位列表
        List<KwlbDTO> kwlbList = new ArrayList<>();
        KwlbDTO kwlb = new KwlbDTO();
        kwlb.setLjkch("TEST_LJKCH_001");
        kwlb.setKch("TEST_KCH_001");
        
        // 构建考生位号详情
        List<KzwhxqDTO> kzwhxqList = new ArrayList<>();
        
        // 测试误识别情况
        KzwhxqDTO kzwhxq1 = new KzwhxqDTO();
        kzwhxq1.setZkzh("TEST_ZKZH_001");
        kzwhxq1.setBpzwh("01");
        kzwhxq1.setSjzwh("01");
        kzwhxq1.setRcbz("1"); // 误识别
        kzwhxq1.setSjyxj("20");
        
        // 构建已上报核验信息
        YsbhyxxDTO ysbhyxx1 = new YsbhyxxDTO();
        ysbhyxx1.setYzsj("2025-01-15 09:00:00");
        ysbhyxx1.setHyjg("1");
        ysbhyxx1.setKddm("TEST_KD_001");
        ysbhyxx1.setSzjg("1");
        ysbhyxx1.setRlsbjg("0");
        ysbhyxx1.setZwrzjg("1");
        kzwhxq1.setYsbhyxx(ysbhyxx1);
        
        // 构建已上报入场信息
        YsbrcxxDTO ysbrcxx1 = new YsbrcxxDTO();
        ysbrcxx1.setSbsj("2025-01-15 09:30:00");
        ysbrcxx1.setKddm("TEST_KD_001");
        ysbrcxx1.setRczt("1");
        kzwhxq1.setYsbrcxx(ysbrcxx1);
        
        kzwhxqList.add(kzwhxq1);
        
        // 测试缺考情况
        KzwhxqDTO kzwhxq2 = new KzwhxqDTO();
        kzwhxq2.setZkzh("TEST_ZKZH_002");
        kzwhxq2.setBpzwh("02");
        kzwhxq2.setSjzwh("02");
        kzwhxq2.setRcbz("6"); // 缺考（空位）
        kzwhxq2.setSjyxj("20");
        kzwhxqList.add(kzwhxq2);
        
        kwlb.setKzwhxq(kzwhxqList);
        kwlbList.add(kwlb);
        sbkkwItem.setKwlb(kwlbList);
        
        dataArray.add(sbkkwItem);
        sbkkwReq.setDataArray(dataArray);
        
        // 模拟加密数据（实际应该是加密的JSON字符串）
        sbkkwReq.setData("encrypted_data_placeholder");
        
        try {
            // 执行测试
            mqttSbkkwEventDbHandler.sbkkw(sbkkwReq);
            log.info("空考位上报测试完成");
        } catch (Exception e) {
            log.error("空考位上报测试失败", e);
        }
    }

    /**
     * 测试空考位上报 - 无考生数据（空数组）
     */
    @Test
    public void testSbkkwWithEmptyData() {
        SbkkwReq sbkkwReq = new SbkkwReq();
        
        List<SbkkwItemDTO> dataArray = new ArrayList<>();
        SbkkwItemDTO sbkkwItem = new SbkkwItemDTO();
        sbkkwItem.setExamPlanCode("TEST_EXAM_002");
        sbkkwItem.setOrgCode("TEST_ORG_002");
        sbkkwItem.setSn("TEST_SN_002");
        sbkkwItem.setDevType("172");
        sbkkwItem.setCcm("002");
        sbkkwItem.setBzhkcid("TEST_BZHKCID_002");
        sbkkwItem.setZcqswzm("01");
        sbkkwItem.setZwbjfsm("01");
        sbkkwItem.setZwplfsm("01");
        sbkkwItem.setCzsj("2025-01-15 11:00:00");
        
        // 空考位列表
        sbkkwItem.setKwlb(new ArrayList<>());
        
        dataArray.add(sbkkwItem);
        sbkkwReq.setDataArray(dataArray);
        sbkkwReq.setData("encrypted_empty_data_placeholder");
        
        try {
            mqttSbkkwEventDbHandler.sbkkw(sbkkwReq);
            log.info("空考位上报（无数据）测试完成");
        } catch (Exception e) {
            log.error("空考位上报（无数据）测试失败", e);
        }
    }

    /**
     * 测试空考位上报 - 无编排考生
     */
    @Test
    public void testSbkkwWithNoArrangedStudents() {
        SbkkwReq sbkkwReq = new SbkkwReq();
        
        List<SbkkwItemDTO> dataArray = new ArrayList<>();
        SbkkwItemDTO sbkkwItem = new SbkkwItemDTO();
        sbkkwItem.setExamPlanCode("TEST_EXAM_003");
        sbkkwItem.setOrgCode("TEST_ORG_003");
        sbkkwItem.setSn("TEST_SN_003");
        sbkkwItem.setDevType("172");
        sbkkwItem.setCcm("003");
        sbkkwItem.setBzhkcid("TEST_BZHKCID_003");
        sbkkwItem.setZcqswzm("01");
        sbkkwItem.setZwbjfsm("01");
        sbkkwItem.setZwplfsm("01");
        sbkkwItem.setCzsj("2025-01-15 11:30:00");
        
        // 有考位但无编排考生
        List<KwlbDTO> kwlbList = new ArrayList<>();
        KwlbDTO kwlb = new KwlbDTO();
        kwlb.setLjkch("TEST_LJKCH_003");
        kwlb.setKch("TEST_KCH_003");
        kwlb.setKzwhxq(new ArrayList<>()); // 空的考生位号详情
        
        kwlbList.add(kwlb);
        sbkkwItem.setKwlb(kwlbList);
        
        dataArray.add(sbkkwItem);
        sbkkwReq.setDataArray(dataArray);
        sbkkwReq.setData("encrypted_no_students_data_placeholder");
        
        try {
            mqttSbkkwEventDbHandler.sbkkw(sbkkwReq);
            log.info("空考位上报（无编排考生）测试完成");
        } catch (Exception e) {
            log.error("空考位上报（无编排考生）测试失败", e);
        }
    }
}
